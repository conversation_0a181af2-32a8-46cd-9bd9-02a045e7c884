[![pipeline status](https://git.gobies.org/shared-platform/foscan/data_analysis/badges/main/pipeline.svg)](https://git.gobies.org/shared-platform/foscan/data_analysis/-/commits/main)
[![coverage report](https://git.gobies.org/shared-platform/foscan/data_analysis/badges/main/coverage.svg)](https://git.gobies.org/shared-platform/foscan/data_analysis/-/commits/main)
[![Latest Release](https://git.gobies.org/shared-platform/foscan/data_analysis/-/badges/release.svg)](https://git.gobies.org/shared-platform/foscan/data_analysis/-/releases)


# data_analysis

data_analysis

## Getting started



# 如果更新规则文件的话，需要更新这个程序，打包cicd


1. unit_test 阶段
   操作：只做测试，不构建镜像
   输出：生成测试覆盖率报告 detail.html
2. developer_test 阶段
   操作：使用 .goreleaser_test.yaml 配置构建
   镜像推送到：harbor.fofa.info/fobase/foscan/develop/data_analysis
3. release 阶段
   操作：使用 .goreleaser.yaml 配置构建（仅在打 tag 时触发）
   镜像推送到：harbor.fofa.info/fobase/foscan/data_analysis
   dockers:
- image_templates:
    - harbor.fofa.info/fobase/foscan/data_analysis
      ids:
    - data_analysis
      总结
      开发环境镜像：harbor.fofa.info/fobase/foscan/develop/data_analysis (每次推送代码)
      生产环境镜像：harbor.fofa.info/fobase/foscan/data_analysis (仅打 tag 时)
      两个镜像都推送到同一个 Harbor 仓库 harbor.fofa.info，但通过路径区分开发和生产环境。