package main

import (
	"data_analysis/internal/config"
	handler2 "data_analysis/internal/handler"
	"data_analysis/internal/sutra"
	"data_analysis/internal/worker"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	_ "github.com/go-micro/plugins/v4/broker/kafka"
	"go-micro.dev/v4"

	_ "github.com/go-micro/plugins/v4/broker/nats"
	_ "github.com/go-micro/plugins/v4/broker/rabbitmq"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	_ "github.com/go-micro/plugins/v4/registry/consul"
	_ "github.com/go-micro/plugins/v4/registry/etcd"
	_ "github.com/go-micro/plugins/v4/registry/nats"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/server"
)

var (
	Version string
	BuildAt string
)

func main() {
	fmt.Println("Version: ", Version)
	fmt.Println("BuildAt: ", BuildAt)

	// Create service
	srv := micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)

	srv.Init(
		micro.Name(constant.ServiceDataAnalysis),
		micro.Version(Version),
	)

	cc := config.ReadConfig()

	if len(cc.NodeID) > 0 {
		_ = srv.Server().Init(server.Id(cc.NodeID))
	}

	err := worker.LoadGeoDb(cc.GeoIPFilename, cc.ASNFilename, "", "")

	if err != nil {
		logger.Fatal("load geo db failed", err)
	}

	s, err := sutra.NewSutra()
	if err != nil {
		logger.Fatal("init sutra failed", err)
	}

	dataAnalysis := handler2.NewDataAnalysis(cc, srv, s)

	// 输出最终的规则统计信息
	logger.Info("=== 规则引擎初始化完成统计 ===")
	logger.Infof("服务版本: %s", Version)
	logger.Infof("构建时间: %s", BuildAt)
	logger.Info("=== 规则加载统计完成 ===")

	// Register handler
	if err := rpcx.RegisterDataAnalysisHandler(srv.Server(), &dataAnalysis); err != nil {
		logger.Fatal(err)
	}

	// RegisterSubscriber
	if err := micro.RegisterSubscriber(srv.Server().Options().Name, srv.Server(), dataAnalysis.HandleEvent, server.SubscriberQueue(srv.Server().Options().Name)); err != nil {
		logger.Fatal(err)
	}

	// Run service
	if err := srv.Run(); err != nil {
		logger.Fatal(err)
	}
}
