package gosutra

import (
	"context"
	"encoding/json"
	"fmt"
	"git.gobies.org/sutra/gosutra/structs"
	"github.com/stretchr/testify/assert"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"
)

var (
	touched     = 0
	queryHander = func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/api/v1/query", "/api/v1/querydetail":
			touched = 1

			var hashes []string
			err := json.NewDecoder(r.Body).Decode(&hashes)
			if err != nil {
				http.Error(w, err.Error(), http.StatusBadRequest)
				return
			}
			switch hashes[0] {
			case "31fd60f56a1dd3da17a586816e2df77f":
				// app no child
				w.Write<PERSON>eader(http.StatusOK)
				w.Write([]byte(`{
   "code": 200,
   "data": {
       "31fd60f56a1dd3da17a586816e2df77f": {
           "has_products": true,
           "products": [
               {
                   "hash":"31fd60f56a1dd3da17a586816e2df77f",
                   "hash_type": 0,
                   "product":"myapp"
               }
           ]
       }
   }
}
`))
				return
			case "ede4a241dec55c7f96620edc64eb94ad":
				// has children
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
   "code": 200,
   "data": {
       "ede4a241dec55c7f96620edc64eb94ad": {
           "has_products": true,
           "products": [
               {
                   "hash":"ede4a241dec55c7f96620edc64eb94ad",
                   "hash_type": 0,
                   "product":"myapp",
                    "children": [
                        {
                            "product": "Apache-httpd",
                            "product_model": "",
                            "product_version": "v2.0.0",
                            "product_zh": "Apache服务器",
                            "product_url": "http://apache.com",
                            "level_code": 3,
                            "country_code": 2,
                            "soft_hard_code": 1
                        }
                    ]
               }
           ]
       }
   }
}
`))
				return
			case "abcb0ba9cefefbb3ad25f955e5ed2e05":
				// known hash 不是黑名单
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
   "code": 200,
   "data": {
       "abcb0ba9cefefbb3ad25f955e5ed2e05": {
            "has_products": false,
            "need_more_hash": false
       }
   }
}
`))
			case "f8f64cfbbbbac1db3b5939c27425675f":
				// known hash 是黑名单
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
   "code": 200,
   "data": {
       "f8f64cfbbbbac1db3b5939c27425675f": {
            "has_products": false,
            "need_more_hash": false,
			"hash_state": 2
       }
   }
}
`))
				return
			case "b5569dd36789b7be89cad28bc3c8a495":
				// unknown hash
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
   "code": 200,
   "data": {
       "b5569dd36789b7be89cad28bc3c8a495": {
            "has_products": false,
            "need_more_hash": true
       }
   }
}
`))
				return
			case "97cf192300b888da473be27d5d84abe1":
				// unknown app hash
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
   "code": 200,
   "data": {
       "97cf192300b888da473be27d5d84abe1": {
            "has_products": false,
            "need_more_hash": true
       }
   }
}
`))
				return
			}
		case "/api/v1/queryhash":
			touched = 2
			var hashes []map[string]interface{}
			err := json.NewDecoder(r.Body).Decode(&hashes)
			if err != nil {
				http.Error(w, err.Error(), http.StatusBadRequest)
				return
			}
			switch hashes[0]["hash"] {
			case "b5569dd36789b7be89cad28bc3c8a495":
				// unknown hash
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
    "code": 200,
    "data": {
        "b5569dd36789b7be89cad28bc3c8a495": {
            "need_more_info": true
        }
    }
}
`))
				return
			case "97cf192300b888da473be27d5d84abe1":
				// unknown myapp hash
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
    "code": 200,
    "data": {
        "97cf192300b888da473be27d5d84abe1": {
            "need_more_info": true
        }
    }
}
`))
				return
			}
		case "/api/v1/queryinfo":
			touched = 3
			var hashes []map[string]interface{}
			err := json.NewDecoder(r.Body).Decode(&hashes)
			if err != nil {
				http.Error(w, err.Error(), http.StatusBadRequest)
				return
			}
			switch hashes[0]["hash"] {
			case "b5569dd36789b7be89cad28bc3c8a495":
				// unknown hash
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
	"code": 200
}
`))
				return
			case "97cf192300b888da473be27d5d84abe1":
				// unknown app hash
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
   "code": 200,
   "data": {
       "97cf192300b888da473be27d5d84abe1": {
            "has_products": true,
            "products": [
                {
                    "hash":"97cf192300b888da473be27d5d84abe1",
                    "hash_type": 0,
                    "product":"myapp"
                }
            ]

       }
   }
}
`))
				return
			}
		}
	}
)

func ExampleClient_Products() {
	c := NewClient(
		WithCloudQuery(false),
		WithRules(context.Background(), `{"product":"testrule","rule":"banner=abcd"}`, false),
	)
	ps, _ := c.Products(`{"banner":"abcd"}`)
	fmt.Println(len(ps))
	fmt.Println(ps[0].Name)
	// Output:
	// 1
	// testrule
}

func TestNewClient(t *testing.T) {

	ts := httptest.NewServer(http.HandlerFunc(queryHander))
	defer ts.Close()

	// 测试不开启云
	touched = 0
	c := NewClient(WithServer(ts.URL))
	c.Products(`{"body":"abc"}`)
	assert.Equal(t, 0, touched)

	// 测试开启云，样本太小，不触发
	touched = 0
	c = NewClient(WithServer(ts.URL), WithCloudQuery(true))
	c.Products(`{"body":"abc"}`)
	assert.Equal(t, 0, touched)

	// 测试开启云，触发，并且有结果
	touched = 0
	c = NewClient(WithServer(ts.URL), WithCloudQuery(true))
	ps, err := c.Products(`{"body":"<html><head></head><body><myapp></myapp></body></html>"}`)
	assert.Nil(t, err)
	assert.Equal(t, 1, touched)
	assert.Equal(t, 1, len(ps))

	// 测试开启queryinfo，有结果，且有children
	touched = 0
	c = NewClient(WithServer(ts.URL), WithCloudQuery(true), WithQueryRawJson(true), WithCloudCallback(func(obj *structs.JsonObj, products []*structs.Product) {
		ps = products
	}))
	ps, err = c.Products(`{"body":"<html><head></head><body><myapp><children></children></myapp></body></html>"}`)
	time.Sleep(time.Millisecond * 100)
	c.Wait()
	assert.Nil(t, err)
	assert.Equal(t, 1, touched)
	assert.Equal(t, 2, len(ps))
	assert.Equal(t, "Apache-httpd", ps[1].Name)

	// 测试开启云，触发，但是没有结果，也不用去进行提交json
	touched = 0
	c = NewClient(WithServer(ts.URL), WithCloudQuery(true), WithCloseLocalEngine(true), WithHashCacheMinute(0))
	ps, err = c.Products(`{"body":"<html><head></head><body><known></known></body></html>"}`)
	assert.Nil(t, err)
	assert.Equal(t, 1, touched)
	assert.Equal(t, 0, len(ps))
	// 这时候本地会作为黑名单处理
	touched = 0
	c = NewClient(WithServer(ts.URL), WithCloudQuery(true), WithCloseLocalEngine(true), WithHashCacheMinute(0))
	ps, err = c.Products(`{"body":"<html><head></head><body><known><black></black></known></body></html>"}`)
	assert.Nil(t, err)
	assert.Equal(t, 0, len(ps))

	// 测试开启云，触发queryhash
	touched = 0
	c = NewClient(WithServer(ts.URL), WithCloudQuery(true))
	ps, err = c.Products(`{"body":"<html><head></head><body><unknown></unknown></body></html>"}`)
	time.Sleep(time.Millisecond * 100)
	c.Wait()
	assert.Nil(t, err)
	assert.Equal(t, 2, touched)
	assert.Equal(t, 0, len(ps))

	// 测试开启queryinfo，没有结果
	touched = 0
	c = NewClient(WithServer(ts.URL), WithCloudQuery(true), WithQueryRawJson(true))
	ps, err = c.Products(`{"body":"<html><head></head><body><unknown></unknown></body></html>"}`)
	time.Sleep(time.Millisecond * 100)
	c.Wait()
	assert.Nil(t, err)
	assert.Equal(t, 3, touched)
	assert.Equal(t, 0, len(ps))

	// 测试开启queryinfo，有结果
	touched = 0
	c = NewClient(WithServer(ts.URL), WithCloudQuery(true), WithQueryRawJson(true), WithCloudCallback(func(obj *structs.JsonObj, products []*structs.Product) {
		ps = products
	}))
	ps, err = c.Products(`{"body":"<html><head></head><body><unknown><myapp/></unknown></body></html>"}`)
	time.Sleep(time.Millisecond * 100)
	c.Wait()
	assert.Nil(t, err)
	assert.Equal(t, 3, touched)
	assert.Equal(t, 1, len(ps))
}

func TestWithHashCacheMinute(t *testing.T) {
	testFilePath := "abc.txt"
	testHash := "24db6980795a4524e6b63ad787459b7f"

	os.Remove(testFilePath)
	c := NewClient(WithHashCacheMinute(10), WithHashCacheFile(testFilePath))
	v, err := c.hashCache.Get(testHash)
	assert.Error(t, err)
	assert.Nil(t, v)

	// 一条hash
	err = ioutil.WriteFile(testFilePath,
		[]byte(`[{"product":"Linksys-IP-Cameras","level":"1","category":"Cameras and Surveillance","parent_category":"Internet of Things (IoT) Device","softhard":"1","company":"","from":"cloud","cached_at":"2022-02-19 20:01:46","ehash":"24db6980795a4524e6b63ad787459b7f"}]`),
		0666)
	assert.Nil(t, err)
	defer os.Remove(testFilePath)
	c = NewClient(WithHashCacheMinute(10), WithHashCacheFile(testFilePath))
	v, err = c.hashCache.Get(testHash)
	assert.Nil(t, err)
	a := v.([]*structs.Product)
	assert.Equal(t, 1, len(a))
	assert.Equal(t, "Linksys-IP-Cameras", v.([]*structs.Product)[0].Name)

	// 同一个hash两条记录
	err = ioutil.WriteFile(testFilePath,
		[]byte(`[{"product":"Linksys-IP-Cameras","level":"1","category":"Cameras and Surveillance","parent_category":"Internet of Things (IoT) Device","softhard":"1","company":"","from":"cloud","cached_at":"2022-02-19 20:01:46","ehash":"24db6980795a4524e6b63ad787459b7f"},{"product":"testProduct","level":"1","category":"Cameras and Surveillance","parent_category":"Internet of Things (IoT) Device","softhard":"1","company":"","from":"cloud","cached_at":"2022-02-19 20:01:46","ehash":"24db6980795a4524e6b63ad787459b7f"}]`),
		0666)
	assert.Nil(t, err)
	defer os.Remove(testFilePath)
	c = NewClient(WithHashCacheMinute(10), WithHashCacheFile(testFilePath))
	v, err = c.hashCache.Get(testHash)
	assert.Nil(t, err)
	a = v.([]*structs.Product)
	assert.Equal(t, 2, len(a))
	assert.Equal(t, "Linksys-IP-Cameras", v.([]*structs.Product)[0].Name)
	assert.Equal(t, "testProduct", v.([]*structs.Product)[1].Name)

}

func TestWithHashCacheFile(t *testing.T) {
	testFilePath := "abc.txt"
	var err error

	touched = 0

	ts := httptest.NewServer(http.HandlerFunc(queryHander))
	defer ts.Close()

	// 黑名单缓存
	err = ioutil.WriteFile(testFilePath,
		[]byte(`[]`),
		0666)
	assert.Nil(t, err)
	defer os.Remove(testFilePath)
	c := NewClient(WithHashCacheMinute(10), WithHashCacheFile(testFilePath))
	_, err = c.hashCache.Get("f8f64cfbbbbac1db3b5939c27425675f")
	assert.NotNil(t, err)
	c = NewClient(WithServer(ts.URL), WithCloudQuery(true), WithQueryRawJson(true))
	ps, err := c.Products(`{"body":"<html><head></head><body><known><black></black></known></body></html>"}`)
	assert.Nil(t, err)
	assert.Equal(t, 0, len(ps))
	//assert.Equal(t, "-", ps[0].Name)
	//assert.Equal(t, "f8f64cfbbbbac1db3b5939c27425675f", ps[0].EHash)
}
