package structs

import (
	"encoding/json"
	"github.com/vmihailenco/msgpack/v5"
)

/*
一个product的属性定义：

	{
		"rule_id":         "1",
		"level":           "1",
		"product":         "test",
		"category":        "cat",
		"parent_category": "pcat",
		"softhard":        "2",
		"company":         "testcompany"
	}
*/
type Product struct {
	Name           string `json:"product"`
	Rule           string `json:"rule,omitempty"`
	RuleId         string `json:"rule_id,omitempty"`
	Level          string `json:"level"`
	Category       string `json:"category"`
	ParentCategory string `json:"parent_category"`
	SoftHard       string `json:"softhard"`
	Company        string `json:"company"`
	Version        string `json:"version"`

	From     string `json:"from"` //来源：local和cloud
	CachedAt string `json:"cached_at"`
	EHash    string `json:"ehash,omitempty"`
}

func NewProductFromLine(line string) (*Product, error) {
	var p Product
	if err := json.Unmarshal([]byte(line), &p); err != nil {
		return nil, err
	}
	return &p, nil
}

func NewProductFromEncryptLine(line string) (*Product, error) {
	var p Product
	if err := msgpack.Unmarshal([]byte(line), &p); err != nil {
		return nil, err
	}
	return &p, nil
}

func NewProductFromMap(m map[string]string) *Product {
	return &Product{
		RuleId:         m["rule_id"],
		Level:          m["level"],
		SoftHard:       m["softhard"],
		Name:           m["product"],
		Company:        m["company"],
		Category:       m["category"],
		ParentCategory: m["parent_category"],
		Version:        m["version"],
		Rule:           m["rule"],
	}
}

func (p *Product) String() string {
	b, _ := json.Marshal(p)
	return string(b)
}

// 复制一份
func (p *Product) Copy() *Product {
	newP := *p
	newP.Rule = ""
	return &newP
}

// 包含具体的版本信息
type ProductVersion struct {
	Model   string
	Version string
	*Product
}
