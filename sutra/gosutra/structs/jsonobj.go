package structs

import (
	stderr "errors"
	"strings"

	"git.gobies.org/sutra/gosutra/algo"
	"git.gobies.org/sutra/gosutra/utils"
	"github.com/pkg/errors"
	"github.com/valyala/fastjson"
)

type ObjectType int

// int mapping
const (
	OtMixed     ObjectType = 0
	OtSubdomain            = 1
	OtService              = 2
)

var (
	ErrSutraJsonObjData = stderr.New("sutra json object error")
)

type StringObjectType int

const (
	StNone   StringObjectType = 0
	StBody   StringObjectType = 1
	StHeader StringObjectType = 2
	StBanner StringObjectType = 3
	StServer StringObjectType = 4
	StTitle  StringObjectType = 5
	StCert   StringObjectType = 6
	StMax    StringObjectType = 7
)

/*
用于快速处理大量使用json的字符串和obj的场景
*/
type JsonObj struct {
	raw          string
	normalized   bool       // 是否进行了归一化处理
	hashed       bool       // 是否生成了hash
	t            ObjectType // 对象的类型，只能是：subdomain或者service
	stringValues [StMax]string
	*fastjson.Value
}

// normalize 归一化处理
// 格式归一化
// 提取ObjectType
// 打入验签：如果有就进行验证；没有就先计算simhash并且打入验签
func (obj *JsonObj) normalize() error {
	if obj.normalized {
		return nil
	}

	// 格式统一
	if v := obj.Get("_type"); v.Exists() {
		// 如果有外围
		switch string(v.GetStringBytes()) {
		case "subdomain":
			obj.t = OtSubdomain
		case "service":
			obj.t = OtService
		default:
			//panic("unknown _index type")
			return stderr.New("unknown _index type")
		}
		obj.Value = obj.Get("_source")
	}

	// 提取类型
	if v := obj.Get("header"); v.Exists() {
		obj.t = OtSubdomain
		obj.stringValues[StHeader] = strings.ToLower(string(v.GetStringBytes()))
	}
	if v := obj.Get("body"); v.Exists() {
		obj.t = OtSubdomain
		obj.stringValues[StBody] = strings.ToLower(string(v.GetStringBytes()))
	}
	if v := obj.Get("banner"); v.Exists() && string(v.GetStringBytes()) != "" {
		obj.t = OtService
		obj.stringValues[StBanner] = strings.ToLower(string(v.GetStringBytes()))
	}
	if v := obj.Get("cert"); v.Exists() {
		obj.stringValues[StCert] = strings.ToLower(string(v.GetStringBytes()))
	}
	if v := obj.Get("title"); v.Exists() {
		obj.stringValues[StTitle] = strings.ToLower(string(v.GetStringBytes()))
	}
	if v := obj.Get("server"); v.Exists() {
		obj.stringValues[StServer] = strings.ToLower(string(v.GetStringBytes()))
	}

	if obj.t == OtMixed {
		//panic("neither subdomain nor service type")
		return errors.WithMessage(ErrSutraJsonObjData, "neither subdomain nor service type")
	}

	obj.normalized = true
	return nil
}

// GenHashes 数据结构增加hash信息
func (obj *JsonObj) GenHashes() error {
	if obj.hashed {
		return nil
	}
	if obj.t == OtSubdomain {
		// 打入验签
		bodyHash := ""
		domHash := ""
		if obj.Get("body_hash").Exists() {
			domHash = string(obj.GetStringBytes("dom_hash"))
			bodyHash = string(obj.GetStringBytes("body_hash"))
		} else {
			// 动态计算
			domHash, bodyHash = utils.BodyHash2(string(obj.GetStringBytes("body")))
		}

		if obj.Get("dom_hash_verified").Exists() {
			if string(obj.GetStringBytes("dom_hash_verified")) != algo.SignString(domHash) {
				return stderr.New("dom_hash_verified not correct")
			}
		} else {
			obj.Set("dom_hash", fastjson.MustParse(`"`+domHash+`"`))
			obj.Set("body_hash", fastjson.MustParse(`"`+bodyHash+`"`))
			obj.Set("dom_hash_verified", fastjson.MustParse(`"`+algo.SignString(domHash)+`"`))
		}
	}
	obj.hashed = true
	return nil
}

// Type 返回类型
func (obj *JsonObj) Type() ObjectType {
	return obj.t
}

// NHash 返回序列号的NHash
func (obj *JsonObj) NHash() string {
	obj.GenHashes()
	return string(obj.GetStringBytes("dom_hash"))
}

// EHash 返回加签名的EHash
func (obj *JsonObj) EHash() string {
	obj.GenHashes()
	return string(obj.GetStringBytes("dom_hash_verified"))
}

// NHash 返回完整的FHash
func (obj *JsonObj) FHash() string {
	obj.GenHashes()
	return string(obj.GetStringBytes("body_hash"))
}

/*
获取字符串
*/
func (obj *JsonObj) StringOfField(field string) string {
	if t := obj.FieldType(field); t == StNone {
		return string(obj.GetStringBytes(field))
	} else {
		return obj.stringValues[obj.FieldType(field)]
	}
}

/*
获取字符串
*/
func (obj *JsonObj) FieldType(field string) StringObjectType {
	switch field {
	case "body":
		return StBody
	case "header":
		return StHeader
	case "banner":
		return StBanner
	case "cert":
		return StCert
	case "title":
		return StTitle
	case "server":
		return StServer
	}
	return StNone
}

func (obj *JsonObj) GetString(key string) string {
	return string(obj.GetStringBytes(key))
}

func NewJsonObj(raw string) (*JsonObj, error) {
	fj, err := fastjson.Parse(raw)
	if err != nil {
		return nil, err
	}
	obj := &JsonObj{
		Value: fj,
		raw:   raw,
	}

	if err := obj.normalize(); err != nil {
		return nil, err
	}

	return obj, nil
}
