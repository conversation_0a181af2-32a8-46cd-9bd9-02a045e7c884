package rulengine

import (
	"git.gobies.org/sutra/gosutra/structs"
	"github.com/stretchr/testify/assert"
	"github.com/valyala/fastjson"
	"testing"
)

func TestRule_NewRuleFromLine(t *testing.T) {
	r, err := newRuleFromLine(`JRun:header="JRun Web Server"`)
	assert.True(t, r.<PERSON>)
	assert.Nil(t, err)

	// 失败的测试
	r, err = newRuleFromLine(`JRun:header="JRun \''Web Server"`)
	assert.Error(t, err)
}

func TestRule_Check(t *testing.T) {
	assert.Equal(t, `name="password`, unEscapeString(`"name=\"password"`))
	assert.Equal(t, `name=\"password`, unEscapeString(`"name=\\\"password"`))

	r, _ := newRuleFromLine(`JRun:header="JRun Web Server"`)
	assert.True(t, r.<PERSON>)

	obj, err := structs.NewJsonObj(`{
  "_index": "fofapro",
  "_type": "subdomain",
  "_id": "***********:13047",
  "_source": {
    "appserver": [
      "jrun web server"
    ],
    "body": "ColdFusion document",
    "domain": "***********",
    "header": "HTTP/1.0 200 OK Date: Sun, 16 Feb 2020 08:24:16 GMT Content-Type: text/html; charset=utf-8 Connection: close Server: JRun Web Server ",
    "host": "***********",
    "hostinfo": "***********:13047",
    "id": "http://***********:13047",
    "infotype": "subdomain",
    "ip": "***********",
    "isdomain": false,
    "port": "13047",
    "server": "JRun Web Server",
    "subdomain": "",
    "title": "Index of /",
    "version": [
      "jrun web server/"
    ]
  }
}`)
	assert.Nil(t, err)
	assert.NotNil(t, obj)

	// 检查
	assert.True(t, r.Check(obj))

	obj, err = structs.NewJsonObj(`{
    "appserver": [
      "jrun web server"
    ],
    "body": "ColdFusion document",
    "domain": "***********",
    "header": "HTTP/1.0 200 OK Date: Sun, 16 Feb 2020 08:24:16 GMT Content-Type: text/html; charset=utf-8 Connection: close Server: JRun Web Server ",
    "host": "***********",
    "hostinfo": "***********:13047",
    "id": "http://***********:13047",
    "infotype": "subdomain",
    "ip": "***********",
    "isdomain": false,
    "port": "13047",
    "server": "JRun Web Server",
    "subdomain": "",
    "title": "Index of /",
    "version": [
      "jrun web server/"
    ]
  }`)
	assert.True(t, r.Check(obj))

	r, _ = newRuleFromLine(`JRun:header!="JRun Web Server"`)
	assert.True(t, r.ParseOk)
	assert.False(t, r.Check(obj))
	r, _ = newRuleFromLine(`JRun:header!="never exists test"`)
	assert.True(t, r.ParseOk)
	assert.True(t, r.Check(obj))

	// regex
	r, _ = newRuleFromLine(`JRun:header~="JRun.*?Server"`)
	assert.True(t, r.ParseOk)
	assert.True(t, r.Check(obj))
	r, _ = newRuleFromLine(`JRun:body~="^ColdFusion"`)
	assert.True(t, r.ParseOk)
	assert.True(t, r.Check(obj))

	r, _ = newRuleFromLine(`JRun:server=="JRun Web Server"`)
	assert.True(t, r.ParseOk)
	assert.True(t, r.Check(obj))

	// &&
	r, _ = newRuleFromLine(`JRun:server="JRun Web Server" && header="JRun"`)
	assert.True(t, r.ParseOk)
	assert.True(t, r.Check(obj))

	// ||
	r, _ = newRuleFromLine(`JRun:server="JRun Web Server" || header="never exists test"`)
	assert.True(t, r.ParseOk)
	assert.True(t, r.Check(obj))
	r, _ = newRuleFromLine(`JRun:server="never exists test" || header="JRun Web Server"`)
	assert.True(t, r.ParseOk)
	assert.True(t, r.Check(obj))

	obj, err = structs.NewJsonObj(`{
  "host": "************:19999",
  "ip": "************",
  "port": "19999",
  "header": "HTTP/1.1 200 OK\r\nConnection: close\r\nContent-Length: 5683\r\nAccess-Control-Allow-Headers: Origin, Content-Type, X-Auth-Token\r\nAccess-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS\r\nAccess-Control-Allow-Origin: *\r\nCache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0\r\nCache-Control: no-cache\r\nContent-Type: text/html; charset=UTF-8\r\nDate: Tue, 07 Dec 2021 08:38:44 GMT\r\nExpires: Thu, 19 Nov 1981 08:52:00 GMT\r\nPragma: no-cache\r\nServer: Apache/2.4.18 (Ubuntu)\r\nSet-Cookie: PHPSESSID=4qijsihdtm0f5i2g9sp0b3epm1; path=/\r\nSet-Cookie: laravel_session=eyJpdiI6IlFFYkxcL2RwM0FEaCtVVUI1Y3pyOGxBPT0iLCJ2YWx1ZSI6IjhRSXhyN3NSYlhqV1llMFwvZVJUQXA0WnlwVDhRc3lRQXVNXC9ZSlVvRERhVHNFXC8yTEk1WTE4ZWs4UjR5K3hEYjFaRVlRZ3laSWZXUmlicHlDS0VCbXNRPT0iLCJtYWMiOiIxYTc2NGQ2ZTgyNGIzMzc3OThiMDhkNTgxNmIxNWZlOWNhNTQ2ZjkxN2QxMzYxYjZlYzhlZGY3ZDI0N2ZkZDQ1In0%3D; expires=Tue, 07-Dec-2021 10:38:44 GMT; Max-Age=7200; path=/; httponly\r\nVary: Accept-Encoding\r\n",
  "body": "<!DOCTYPE html>\n<html>\n\n<head>\n\n    <meta charset=\"utf-8\">\n    <meta name=\"csrf-token\" content=\"ssPrHF40slnKo65EdL0QGFZOnsILMKHChOU1xk6T\" />\n    <title>SISTER</title>\n    <link rel=\"icon\" type=\"image/png\" href=\"http://************:19999/favicon.png\"/>\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n    <title>INSPINIA | Login 2</title>\n\n    <link href=\"http://************:19999/css/bootstrap.min.css\" rel=\"stylesheet\">\n    <link href=\"http://************:19999/font-awesome/css/font-awesome.min.css\" rel=\"stylesheet\">\n    <!-- <link href=\"http://************:19999/css/animate.css\" rel=\"stylesheet\"> -->\n    <link rel=\"stylesheet\" href=\"http://************:19999/bower_components/sweetalert/dist/sweetalert.css\">\n    <script type=\"text/javascript\" src=\"http://************:19999/js/jquery-2.1.1.js\"></script>\n\n    <link href=\"http://************:19999/css/plugins/toastr/toastr.min.css\" rel=\"stylesheet\">\n\t<script src=\"http://************:19999/js/plugins/toastr/toastr.min.js\"></script>\n\n\t<style type=\"text/css\">\n\t\t#toast-container > .toast-error:before{\n\t\t\tcontent: '';\n\t\t}\n\t</style>\n\t\n\t<script type=\"text/javascript\">\n\t\t$(function () {\n\t\t\ttoastr.options = {\n\t\t\t  \"closeButton\": true,\n\t\t\t  \"debug\": false,\n\t\t\t  \"progressBar\": true,\n\t\t\t  \"preventDuplicates\": false,\n\t\t\t  \"positionClass\": \"toast-top-right\",\n\t\t\t  \"onclick\": null,\n\t\t\t  \"showDuration\": 400,\n\t\t\t  \"hideDuration\": 1000,\n\t\t\t  \"timeOut\": 7000,\n\t\t\t  \"extendedTimeOut\": 1000,\n\t\t\t  \"showEasing\": \"swing\",\n\t\t\t  \"hideEasing\": \"linear\",\n\t\t\t  \"showMethod\": \"fadeIn\",\n\t\t\t  \"hideMethod\": \"fadeOut\"\n\t\t\t}\n\t\t});\n\t</script>\n    <link href=\"http://************:19999/css/plugins/iCheck/custom.css\" rel=\"stylesheet\">\n<script src=\"http://************:19999/js/plugins/iCheck/icheck.min.js\"></script>\n\n<script type=\"text/javascript\">\n\tvar initialize_icheck = function(){\n\t\t$('.i-checks').iCheck({\n            checkboxClass: 'icheckbox_square-green',\n            radioClass: 'iradio_square-green',\n        });\n\t}\n\n    $(document).ready(function () {\n    \tinitialize_icheck();    \n    });\n</script>    \n\n    <link href=\"http://************:19999/eak/ejs/css/classy.css\" rel=\"stylesheet\">\n    <link href=\"http://************:19999/css/style.css\" rel=\"stylesheet\">\n    <link href=\"http://************:19999/css/login.css\" rel=\"stylesheet\">\n\n\n</head>\n\n<body class=\"gray-bg bg-cover\" style=\"background: url(http://************:19999/bg_login_new.jpg) no-repeat fixed;;\">\n\n    \n    <div class=\"middle-box animated fadeInDown\">\n        <div class=\"abs-bg\"></div>\n\n        <div class=\"content loginscreen\">\n            <div class=\"text-center\">\n                <div>\n                    <img src=\"http://************:19999/logo.png\" class=\"logo\" />\n                </div>\n                <p class=\"new-logo-name\" style=\"margin: 0px\">\n                    SISTER<br>\n                    <p style=\"font-size: small;\"><b>Sistem Informasi Sumberdaya Terintegrasi</b></p>\n                </p>\n                                    <h3><b>IAIN Ambon</b></h3>\n                            </div>\n\n                <form method=\"post\" action=\"http://************:19999/auth/login\" class=\"m-t\" role=\"form\">\n        <input type=\"hidden\" name=\"_token\" value=\"ssPrHF40slnKo65EdL0QGFZOnsILMKHChOU1xk6T\">\n        \n        <div class=\"form-group \">\n            <label class=\"control-label\">USERNAME</label>\n            <input type=\"text\" name=\"username\" value=\"\" class=\"form-control\" placeholder=\"Tulis username/email anda...\">\n        </div>\n        <div class=\"form-group  \">\n            <label class=\"control-label\">PASSWORD</label>\n            <input type=\"password\" name=\"password\" id=\"password\" class=\"form-control\" placeholder=\"Tulis password anda...\">\n        </div>\n        \n        <button type=\"submit\" class=\"btn btn-success block full-width m-b noborder-radius\"><b>LOGIN</b></button>\n\n        <table class=\"table\">\n            <tbody>\n                <tr>\n                    <td>\n                        <div style=\"text-align: left;\">\n                            <label class=\"control-label\">\n                                <h5><a href=\"http://************:19999/password/lupa\">Lupa password?</a></h5>\n                            </label>\n                        </div>\n                    </td>\n                    <td>\n                        <div style=\"text-align: right;\">\n                            <label class=\"control-label\">\n                                <h5>Belum memiliki akun?<a href=\"http://************:19999/registrasi\"> Daftar di sini.</a></h5>\n                            </label>\n                        </div>\n                    </td>\n                </tr>\n                <tr>\n                    <td>\n                        <div style=\"text-align: left;\"><h4><a href=\"http://************:19999/panduan\" target=\"_blank\" ><i class=\"fa fa-download\"></i> Unduh Panduan</a></h4></td>\n                            </div>\n                    <td><div style=\"text-align: right;\">\n                        <span style=\"font-size: 0.7em\">\n                    <i class=\"fa fa-envelope\"></i>\n                </span>\n                <span style=\"font-size: 0.75em\">\n                    <EMAIL>\n                </span>\n                    </div></td>\n                </tr>\n            </tbody>\n        </table>\n    </form>\n\n            <p class=\"m-t text-center\"> \n                <small>\n                    <b>Direktorat Sumber Daya - Direktorat Jenderal Pendidikan Tinggi, Riset dan Teknologi</b>\n                </small>\n            </p>\n        </div>\n    </div>\n    <script src=\"http://************:19999/bower_components/sweetalert/dist/sweetalert.min.js\"></script>\n</body>\n\n</html>\n",
  "nhash": "-4246898293106848714",
  "ehash": "0aa2f3630eda43fe20d7ef9458e0bea5",
  "lastupdatetime": "2021-12-07 07:40:42"
}`)
	assert.Nil(t, err)
	r, _ = newRuleFromLine(`Inspinia:body="name=\"password" && body="Inspinia"`)
	assert.True(t, r.ParseOk)
	assert.True(t, r.Check(obj))

	//debugCheckDetail = true
	a := `
{
  "host": "*************:1000",
  "data": "{\"host\":\"*************:1000\",\"ip\":\"*************\",\"port\":\"1000\",\"header\":\"HTTP/1.0 200 OK\\r\\nConnection: close\\r\\nContent-Type: text/html; charset=utf-8\\r\\nDate: Tue, 05 May 2020 20:27:53 GMT\\r\\nServer: Httpd/1.0\\r\\n\",\"body\":\"\\u003cHTML\\u003e\\u003cHEAD\\u003e\\u003cTITLE\\u003eipTIME A2004NS\\u003c/TITLE\\u003e\\n\\u003cscript\\u003e\\n\\n//login_navi\\nfunction login_session()\\n{\\n\\t// Check ID Password \\n\\n\\t// Check captcha if exist\\n\\t//location.href = \\\"../testbin/login.cgi\\\";\\n\\t//\\n\\tdocument.form.submit();\\n}\\n\\n\\u003c/script\\u003e\\n\\u003cSCRIPT type=\\\"text/javascript\\\"\\u003e\\n\\u003c!--\\nfunction LoginProcess()\\n\\t\\t{\\n\\t\\t\\tif(document.form.captcha_on.value == '1')\\t\\t\\t\\tdocument.form.captcha_file.value=iframe_captcha.document.captcha_form.captcha_file.value;\\n\\t\\t\\tdocument.form.submit();\\n\\t\\t}\\nfunction ChangeCaptchaUse(flag)\\n        {\\n                if(flag==0)\\n                {\\n                        with(document)\\n                        {\\n                                getElementById(\\\"captchatr1\\\").style.display = \\\"none\\\";\\n                                getElementById(\\\"captchatr2\\\").style.display = \\\"none\\\";\\n                                getElementById(\\\"captchatr3\\\").style.display = \\\"none\\\";\\n                        }\\n                }\\n                else\\n                        ChangeCaptchaInputBg();\\n        }\\nfunction ChangeCaptchaInputBg(flag)\\n        {\\n                if(flag==\\\"clear\\\")\\n                        document.form.captcha_code.style.cssText=\\\"background-image:url();width:255px; height:21px;\\\";\\n                else\\n                        document.form.captcha_code.style.cssText=\\\"background-image:url(/images2/login_str_captcha_bg.kr.gif);width:255px; height:21px;\\\";\\n        }\\nfunction FocusPassword()\\n\\t{\\n        var F = document.form;\\n        if(F.passwd.value == F.default_passwd.value)\\n        {\\n\\t\\tdocument.getElementById(\\\"passwd_td\\\").innerHTML = '\\u003cinput type=password name=passwd CLASS=login_input TABINDEX=2\\u003e';\\n                document.form.passwd.value = '';\\n                document.form.passwd.style.color = \\\"#000000\\\";\\n                document.form.passwd.focus();\\n        }\\n\\t}\\nfunction RenewCaptchaImage()\\n        {\\n                document.getElementById(\\\"iframe_captcha\\\").contentWindow.location.href=\\\"/sess-bin/captcha.cgi\\\";\\n        }\\n\\t//--\\u003e\\n\\t\\u003c/SCRIPT\\u003e\\n\\u003cSTYLE\\u003e\\n.item_text { font: normal normal normal 12px arial; color:#000000; }\\n\\ntable,td { font: normal normal normal 12px arial; color:#000000; }\\nbody\\n{\\n        scrollbar-face-color:#FFFFFF;\\n        scrollbar-highlight-color: #aaaaaa;\\n        scrollbar-3dlight-color: #FFFFFF;\\n        scrollbar-shadow-color: #aaaaaa;\\n        scrollbar-darkshadow-color: #FFFFFF;\\n        scrollbar-track-color: #FFFFFF;\\n        scrollbar-arrow-color: #aaaaaa;\\n        font: normal normal normal 12px arial;\\n}\\n\\n/*modification*/\\n.login_info_td {height:49; padding:0px 17px 0px 0px; border-collapse:collapse;}\\n\\n/*new*/\\n.login_input { font: normal normal normal 12px arial; color:#000000; border:#C6C9CC 1px solid; width:181px; height:21px;}\\n\\n\\n\\u003c/STYLE\\u003e\\n\\n\\n\\u003c/HEAD\\u003e\\n\\u003cBODY \\u003e\\n\\u003cFORM NAME=\\\"form\\\" METHOD=\\\"post\\\" ACTION=\\\"/sess-bin/login_handler.cgi\\\"\\u003e\\n\\u003cINPUT TYPE=hidden NAME=\\\"init_status\\\" value=1\\u003e\\n\\u003cINPUT TYPE=hidden NAME=\\\"captcha_on\\\" value=1\\u003e\\n\\u003cINPUT TYPE=hidden NAME=\\\"captcha_file\\\"\\u003e\\n\\u003cTABLE CELLPADDING=0 CELLSPACING=0 CLASS=navi_login_table ALIGN=CENTER border=0\\u003e\\n\\u003cTR\\u003e\\u003cTD\\u003e\\u003cIMG SRC=\\\"/images2/login_title.a2004ns.gif\\\" BORDER=0\\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\n\\u003cTR\\u003e\\u003cTD BACKGROUND=\\\"/images2/login_main_bg.gif\\\" STYLE=\\\"vertical-align:top;padding:0 0 0 3px;\\\"\\u003e\\n\\u003cTABLE CELLPADDING=0 CELLSPACING=0 WIDTH=258 ALIGN=CENTER\\u003e\\n\\u003cTR\\u003e\\u003cTD COLSPAN=2 HEIGHT=10\\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\n\\u003cTR\\u003e\\u003cTD WIDTH=70 STYLE=\\\"padding:0 0 0 1px;\\\"\\u003e\\u003cIMG SRC=\\\"/images2/login_str_id.kr.gif\\\" BORDER=0\\u003e\\u003c/TD\\u003e\\n\\u003cTD\\u003e\\u003cINPUT type=text name=username CLASS=login_input TABINDEX=1 value=\\\"\\\" onkeydown=\\\"if (event.keyCode == 13) LoginProcess();\\\"\\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\n\\u003cTR\\u003e\\u003cTD COLSPAN=2 HEIGHT=3\\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\n\\u003cTR\\u003e\\u003cTD WIDTH=70 STYLE=\\\"padding:0 0 0 1px;\\\"\\u003e\\u003cIMG SRC=\\\"/images2/login_str_passwd.kr.gif\\\" BORDER=0\\u003e\\u003c/TD\\u003e\\n\\u003cTD ID='passwd_td'\\u003e\\u003cINPUT type=password name=passwd CLASS=login_input TABINDEX=2 onfocus=\\\"FocusPassword();\\\" value=\\\"\\\" style=\\\"color:#000000\\\" onkeydown=\\\"if (event.keyCode == 13) LoginProcess();\\\"\\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\n\\u003cinput type=hidden name=\\\"default_passwd\\\" value=\\\"초기암호:admin(변경필요)\\\"\\u003e\\u003cTR\\u003e\\u003cTD COLSPAN=2 HEIGHT=8\\u003e\\u003c/e\\u003c/TR\\u003e\\n\\u003cTR ID=captchatr1\\u003e\\u003cTD COLSPAN=2\\u003e\\u003cINPUT type=text name=captcha_code CLASS=login_input STYLE=\\\"width:255px; height:21px;\\\" autocomplete='off' autocorrect= 'off' autocapitalize = 'off' spellcheck = 'false' ONFOCUS=\\\"ChangeCaptchaInputBg('clear');\\\" TABINDEX=3 onkeydown=\\\"if (event.keyCode == 13) LoginProcess();\\\"\\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\n\\u003cTR ID=captchatr2\\u003e\\u003cTD COLSPAN=2 HEIGHT=5\\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\n\\u003cTR ID=captchatr3\\u003e\\u003cTD COLSPAN=2 HEIGHT=72\\u003e\\n\\u003cTABLE CELLPADDING=0 CELLSPACING=0 HEIGHT=72 CLASS=\\\"login_input\\\"\\u003e\\n\\u003cTR\\u003e\\u003cTD WIDTH=25\\u003e\\u003cIMG SRC=\\\"/images2/login_bt_refresh.kr.gif\\\" BORDER=0 ONCLICK=\\\"RenewCaptchaImage();\\\" STYLE=\\\"cursor:pointer;\\\"\\u003e\\u003c/TD\\u003e\\n\\u003cTD\\u003e\\u003cIFRAME NAME=iframe_captcha ID=iframe_captcha SRC=\\\"/sess-bin/captcha.cgi\\\" WIDTH=201 HEIGHT=70 FRAMEBORDER=no SCROLLING=no\\u003e\\u003c/IFRAME\\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\n\\u003c/TABLE\\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\n\\u003cTR\\u003e\\u003cTD COLSPAN=2 HEIGHT=12\\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\n\\u003cTR\\u003e\\u003cTD COLSPAN=2 HEIGHT=50\\u003e\\u003cIMG style=\\\"cursor:pointer\\\" SRC=\\\"/images2/login_bt.newwizard.kr.gif\\\" TABINDEX=4 ID=\\\"submit_bt\\\" onclick=\\\"LoginProcess();\\\"  \\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\n\\u003cTR\\u003e\\u003cTD COLSPAN=2 HEIGHT=10\\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\n\\u003c/TABLE\\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\n\\u003cTR\\u003e\\u003cTD HEIGHT=10 BACKGROUND=\\\"/images2/login_sess_back_info.gif\\\"\\u003e\\u003c/TD\\u003e\\u003c/TR\\u003e\\u003c/TABLE\\u003e\\n\\u003c/FORM\\u003e\\n\\u003c/BODY\\u003e\\n\\u003cSCRIPT type=\\\"text/javascript\\\"\\u003e\\n\\u003c!--\\nwith(document)\\n{\\nChangeCaptchaUse(form.captcha_on.value);\\nform.username.focus();\\n}\\n//--\\u003e\\n\\u003c/SCRIPT\\u003e\\n\\u003c/HTML\\u003e\\n\",\"nhash\":\"-5514426776994774611\",\"ehash\":\"09cf45e569d38ab55f019db4b6eadd3e\",\"lastupdatetime\":\"2020-05-06 04:28:07\"}",
  "lastupdatetime": "2020-05-06 04:28:07",
  "raw_cost": "31.032313ms",
  "raw_products": [
    "EFM-Networks-ipTIME-A2004NS"
  ],
  "cost": "514.950499ms",
  "products": [
    "Apache-Web-Server",
    "EFM-Networks-ipTIME-A2004NS"
  ],
  "more": [
    "Apache-Web-Server"
  ],
  "less": null
}
`
	rawdata := fastjson.GetString([]byte(a), "data")
	obj, err = structs.NewJsonObj(rawdata)
	assert.Nil(t, err)
	p, err := structs.NewProductFromLine(`{"product":"Apache-Web-Server","rule":"((header=\"Server: httpd\" || (server=\"Apache\" \u0026\u0026 header!=\"Apache-Coyote\")) \u0026\u0026 header!=\"couchdb\" \u0026\u0026 header!=\"drupal\" \u0026\u0026 body!=\"\u003ch2\u003eMy Resource\u003c/h2\u003e\" \u0026\u0026 body!=\"Server: CouchDB\" \u0026\u0026 header!=\"ReeCam IP Camera\" \u0026\u0026 header!=\"Apache,Tomcat,Jboss\") || (banner=\"Server: httpd\" || (banner=\"Server: Apache\" \u0026\u0026 banner!=\"Apache-Coyote\") \u0026\u0026 banner!=\"couchdb\" \u0026\u0026 banner!=\"drupal\" \u0026\u0026 banner!=\"ReeCam IP Camera\")","rule_id":"211","level":"3","category":"Service","parent_category":"Support System","softhard":"2","company":"Apache Software Foundation."}`)
	assert.Nil(t, err)
	assert.Equal(t, `((header="Server: httpd" || (server="Apache" && header!="Apache-Coyote")) && header!="couchdb" && header!="drupal" && body!="<h2>My Resource</h2>" && body!="Server: CouchDB" && header!="ReeCam IP Camera" && header!="Apache,Tomcat,Jboss") || (banner="Server: httpd" || (banner="Server: Apache" && banner!="Apache-Coyote") && banner!="couchdb" && banner!="drupal" && banner!="ReeCam IP Camera")`, p.Rule)
	r, _ = newRuleFromLine(p.Name + ":" + p.Rule)
	assert.True(t, r.ParseOk)
	assert.True(t, r.Check(obj))
}

func BenchmarkRuleTCheckField(b *testing.B) {
	testData := `{
  "_index": "fofapro",
  "_type": "subdomain",
  "_id": "***********:13047",
  "_source": {
    "appserver": [
      "jrun web server"
    ],
    "body": "ColdFusion document",
    "domain": "***********",
    "header": "HTTP/1.0 200 OK Date: Sun, 16 Feb 2020 08:24:16 GMT Content-Type: text/html; charset=utf-8 Connection: close Server: JRun Web Server ",
    "host": "***********",
    "hostinfo": "***********:13047",
    "id": "http://***********:13047",
    "infotype": "subdomain",
    "ip": "***********",
    "isdomain": false,
    "port": "13047",
    "server": "JRun Web Server",
    "subdomain": "",
    "title": "Index of /",
    "version": [
      "jrun web server/"
    ]
  }
}`
	obj, _ := structs.NewJsonObj(testData)
	r, _ := newRuleFromLine(`JRun:header="JRun Web Server"`)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		r.Check(obj)
	}
}

func BenchmarkRuleTCheckNoField(b *testing.B) {
	testData := `{
  "_index": "fofapro",
  "_type": "subdomain",
  "_id": "***********:13047",
  "_source": {
    "appserver": [
      "jrun web server"
    ],
    "body": "ColdFusion document",
    "domain": "***********",
    "header": "HTTP/1.0 200 OK Date: Sun, 16 Feb 2020 08:24:16 GMT Content-Type: text/html; charset=utf-8 Connection: close Server: JRun Web Server ",
    "host": "***********",
    "hostinfo": "***********:13047",
    "id": "http://***********:13047",
    "infotype": "subdomain",
    "ip": "***********",
    "isdomain": false,
    "port": "13047",
    "server": "JRun Web Server",
    "subdomain": "",
    "title": "Index of /",
    "version": [
      "jrun web server/"
    ]
  }
}`
	obj, _ := structs.NewJsonObj(testData)
	r, _ := newRuleFromLine(`JRun:banner="JRun Web Server"`)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		r.Check(obj)
	}
}
