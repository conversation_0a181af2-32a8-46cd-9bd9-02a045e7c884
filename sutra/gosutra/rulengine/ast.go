package rulengine

import parsec "github.com/prataprc/goparsec"

var (
	globalAst    *parsec.AST
	globalParser parsec.Parser
)

func init() {
	globalAst, globalParser = genAst()
}

func genAst() (*parsec.AST, parsec.Parser) {
	ast := parsec.NewAST("fofaquery", 100)

	space := parsec.Token(`\s`, "SPACE")
	spaceMaybe := parsec.Maybe(nil, space)
	//text := parsec.Token(`[a-zA-Z0-9\-_]*?`, "TEXT")
	left_parenthesis := parsec.Atom("(", "left_parenthesis")
	right_parenthesis := parsec.Atom(")", "right_parenthesis")

	//# Comparisonss
	fulleq := parsec.Atom("==", "fulleq")
	eq := parsec.Atom("=", "eq")
	not_eq := parsec.Atom("!=", "not_eq")
	matches := parsec.Atom("~=", "matches")
	lt := parsec.Atom("<", "lt")
	lteq := parsec.Atom("<=", "lteq")
	gt := parsec.Atom(">", "gt")
	gteq := parsec.Atom(">=", "gteq")
	notContains := parsec.Atom("-", "not_contains")
	notContainsMaybe := parsec.Maybe(nil, notContains)

	//# Operators
	and_operator := parsec.Atom("&&", "and_operator")
	or_operator := parsec.Atom("||", "or_operator")
	//operator := ast.OrdChoice("operator", func(name string, s parsec.Scanner, node parsec.Queryable) parsec.Queryable {
	//	return node
	//}, and_operator, or_operator)

	//# Operand
	null := parsec.Atom("null", "null")
	boolean := ast.OrdChoice("boolean", nil, parsec.Atom("true", "true"), parsec.Atom("false", "false"))
	number := parsec.Token(`[-+]?([0-9]*\.)?[0-9]+$`, "number")
	gantan := parsec.Atom("!", "null")
	//doubleQuote := parsec.Atom(`"`, "DOUBLEQUOTE")
	//doubleQuoteValue := parsec.Token(`(?:\\"|.)*`, "DOUBLEQUOTEVALUE")
	//doubleQuoteString := ast.And("DOUBLEQUOTESTRING", nil, doubleQuote, doubleQuoteValue, doubleQuote)
	doubleQuoteString := parsec.Token(`"(?:\\"|.)*?"`, "DOUBLEQUOTESTRING")
	literal := parsec.Token(`[a-zA-Z0-9\-_\.\p{L}]+`, "literal")
	identifier := ast.OrdChoice("identifier", nil, null, boolean, number, literal, doubleQuoteString, gantan)

	//# Grammar
	compare_fulleq := ast.And("compare_fulleq", nil, literal, spaceMaybe, fulleq, spaceMaybe, identifier)
	compare_eq := ast.And("compare_eq", nil, literal, spaceMaybe, eq, spaceMaybe, identifier)
	compare_not_eq := ast.And("compare_not_eq", nil, literal, spaceMaybe, not_eq, spaceMaybe, identifier)
	compare_matches := ast.And("compare_matches", nil, literal, spaceMaybe, matches, spaceMaybe, identifier)
	compare_lt := ast.And("compare_lt", nil, literal, spaceMaybe, lt, spaceMaybe, identifier)
	compare_lteq := ast.And("compare_lteq", nil, literal, spaceMaybe, lteq, spaceMaybe, identifier)
	compare_gt := ast.And("compare_gt", nil, literal, spaceMaybe, gt, spaceMaybe, identifier)
	compare_gteq := ast.And("compare_gteq", nil, literal, spaceMaybe, gteq, spaceMaybe, identifier)
	compare_contains := ast.And("compare_contains", nil, notContainsMaybe, identifier)

	//var or_operation parsec.Parser // forward declaration allows for recursive parsing
	//var and_operation parsec.Parser // forward declaration allows for recursive parsing

	//var primary parsec.Parser
	var or_operation parsec.Parser
	var and_operation parsec.Parser
	compare := ast.OrdChoice("compare", nil, compare_fulleq, compare_eq, compare_not_eq, compare_matches, compare_lteq, compare_lt, compare_gteq, compare_gt, compare_contains)
	parenthesis := ast.And("parenthesis", nil, left_parenthesis, spaceMaybe, &or_operation, spaceMaybe, right_parenthesis)
	primary := ast.OrdChoice("primary", nil, parenthesis, compare)
	and_operation_item := ast.And("and_operation_item", nil, primary, spaceMaybe, and_operator, spaceMaybe, &and_operation)
	and_operation = ast.OrdChoice("and_operation_item", nil, and_operation_item, primary)
	or_operation_item := ast.And("or_operation_item", nil, and_operation, spaceMaybe, or_operator, spaceMaybe, &or_operation)
	or_operation = ast.OrdChoice("or_operation", nil, or_operation_item, and_operation)
	//
	//primary = ast.OrdChoice("primary", nil, parenthesis, operation_item, compare)
	//root := ast.ManyUntil("root", nil, primary, ast.End("eof"))
	return ast, or_operation
}

func parseToNodes(query string) []parsec.Queryable {
	//log.Println(query)
	r, _ := globalAst.Parsewith(globalParser, parsec.NewScanner([]byte(query)))
	return r.GetChildren()
}
