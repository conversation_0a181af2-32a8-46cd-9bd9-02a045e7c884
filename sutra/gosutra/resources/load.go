package resources

import "embed"

//go:embed rules.json
var ResFs embed.FS

/*
分为三类：
一）server中提取服务器类型，如apache/nginx等
```
cat rules_raw.json | jq 'select(.rule |= ascii_downcase | .rule | contains("server:") or contains("server=" ))' -c > server1.json
replace: | && header!=\"Couchdb\" && header!=\"drupal\"| -> ||
replace: | && header!=\"Couchdb\"| -> ||
replace: | && header!=\"couchdb\"| -> ||
replace: | && header!=\"drupal\"| -> ||
replace: | && banner!=\"Couchdb\"| -> ||
replace: | && banner!=\"couchdb\"| -> ||
replace: | && banner!=\"drupal\"| -> ||
replace: | && body!=\"Server: couchdb\"| -> ||
排序
cat server.json |  jq -s -c 'sort_by(.product)[]' > server3.json

```
二）header中提取操作系统或者支撑层，如ruby/php等，以及云服务？（比如 https://fofapro.com/result?qbase64=aGVhZGVyPUJDMTM1X2x0 对应 https://www.baishan.com/pages/about/about/）
三）body中提取支撑层，如echart等


应该考虑排他性的情况，比如一个server已经有了apache，就不用再测试nginx/iis了
*/
