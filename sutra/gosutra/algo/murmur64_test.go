package algo

import (
	"crypto/md5"
	"encoding/hex"
	"github.com/stretchr/testify/assert"
	"strconv"
	"testing"
)

func TestMurmur3Hash(t *testing.T) {
	var i int32 = -10
	assert.Equal(t, "-10", strconv.Itoa(int(i)))
	assert.Equal(t, "-10", strconv.FormatInt(int64(i), 10))
	assert.Equal(t, "-10", strconv.FormatInt(int64(uint64(i)), 10))
	assert.Equal(t, "4294967286", strconv.FormatInt(int64(uint32(i)), 10))
	// 从上面的结论可以看出来：int在往更大的类型转换的时候，不会丢失符号；相同大小的数据格式的时候，符号就丢失了
	// constant -156908512 overflows uint32
	// constant 4138058784 overflows int32

	var v uint32 = 4138058784
	var vn int32 = -156908512
	assert.Equal(t, vn, int32(v))
	assert.Equal(t, v, uint32(vn))
	assert.Equal(t, "-156908512", Uint64ToInt64String(uint64(int32(v))))
	//assert.Equal(t, "-156908512", Murmur3Hash32("foo"))
	assert.Equal(t, "-2129773440516405919", Murmur3Hash("foo"))
	assert.Equal(t, "-983632591049499078", Murmur3Hash("abcdefghijklmnopqrstuvwxyz0123456789"))
	/*
		$ python3
		Python 3.7.1 (v3.7.1:260ec2c36a, Oct 20 2018, 03:13:28)
		>>> import mmh3
		>>> mmh3.hash("foo")
		-156908512
		>>> mmh3.hash("foo", signed=False)
		4138058784
		>>> mmh3.hash64("foo", signed=False)
		(16316970633193145697, 9128664383759220103)
		>>> mmh3.hash64("foo")
		(-2129773440516405919, 9128664383759220103)
		>>> mmh3.hash64("abcdefghijklmnopqrstuvwxyz0123456789")
		(-983632591049499078, 4149707753321764032)
	*/

	//mmh32会发生冲突的情况
	e1 := `DOCTYPE>html>head>meta>title>/title>meta>meta>meta>meta>meta>meta>meta>meta>link>link>link>link>script>/script>/head>body>link>tt>/tt>var>/var>area>/area>div>map>/map>bdo>/bdo>dfn>/dfn>div>font>/font>ins>/ins>small>/small>div>a>img>/a>/div>sup>/sup>time>/time>tt>/tt>div>ul>li>a>span>/span>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>var>/var>area>/area>map>/map>div>bdo>/bdo>dfn>/dfn>font>/font>div>/div>/div>script>/script>script>/script>/div>/div>ins>/ins>small>/small>sup>/sup>div>a>img>/a>/div>time>/time>tt>/tt>var>/var>div>/div>area>/area>map>/map>bdo>/bdo>div>a>img>/a>dfn>/dfn>font>/font>ins>/ins>div>/div>small>/small>sup>/sup>time>/time>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>script>/script>tt>/tt>var>/var>area>/area>div>/div>map>/map>bdo>/bdo>dfn>/dfn>div>font>/font>ins>/ins>small>/small>div>sup>/sup>time>/time>tt>/tt>div>p>/p>p>/p>var>/var>area>/area>map>/map>div>bdo>/bdo>dfn>/dfn>font>/font>div>p>/p>p>/p>p>/p>p>/p>a>ins>/ins>small>/small>sup>/sup>div>/div>/a>/div>time>/time>tt>/tt>var>/var>div>p>/p>p>/p>p>/p>p>/p>a>area>/area>map>/map>bdo>/bdo>div>/div>/a>/div>dfn>/dfn>font>/font>ins>/ins>div>p>/p>p>/p>p>/p>p>/p>a>small>/small>sup>/sup>time>/time>div>/div>/a>/div>tt>/tt>var>/var>area>/area>div>p>/p>p>/p>p>/p>p>/p>a>map>/map>bdo>/bdo>dfn>/dfn>div>/div>/a>/div>/div>font>/font>ins>/ins>small>/small>div>a>div>/div>/a>a>div>/div>/a>a>div>/div>/a>a>div>/div>/a>/div>sup>/sup>time>/time>tt>/tt>div>ul>li>/li>li>br>/li>li>br>/li>li>br>/li>li>br>/li>/ul>/div>/div>/div>/div>var>/var>area>/area>map>/map>div>bdo>/bdo>dfn>/dfn>font>/font>div>ins>/ins>small>/small>sup>/sup>div>h>/h>h>/h>/div>time>/time>tt>/tt>var>/var>ul>li>area>/area>map>/map>bdo>/bdo>div>dfn>/dfn>font>/font>ins>/ins>div>/div>h>/h>span>/span>/div>/li>li>small>/small>sup>/sup>time>/time>div>tt>/tt>var>/var>area>/area>div>/div>h>/h>span>/span>/div>/li>li>map>/map>bdo>/bdo>dfn>/dfn>div>font>/font>ins>/ins>small>/small>div>/div>h>/h>span>/span>/div>/li>li>sup>/sup>time>/time>tt>/tt>div>var>/var>area>/area>map>/map>div>/div>h>/h>span>/span>/div>/li>li>bdo>/bdo>dfn>/dfn>font>/font>div>ins>/ins>small>/small>sup>/sup>div>/div>h>/h>span>/span>/div>/li>li>time>/time>tt>/tt>var>/var>div>area>/area>map>/map>bdo>/bdo>div>/div>h>/h>span>/span>/div>/li>/ul>/div>/div>dfn>/dfn>font>/font>ins>/ins>div>/div>small>/small>sup>/sup>time>/time>div>tt>/tt>var>/var>area>/area>div>/div>map>/map>bdo>/bdo>dfn>/dfn>div>font>/font>ins>/ins>small>/small>div>dl>dt>/dt>dd>sup>/sup>time>/time>tt>/tt>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/dd>/dl>dl>dt>/dt>dd>var>/var>area>/area>map>/map>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/dd>/dl>dl>dt>/dt>dd>bdo>/bdo>dfn>/dfn>font>/font>ul>li>a>/a>/li>/ul>/dd>/dl>ins>/ins>small>/small>sup>/sup>dl>dt>/dt>dd>p>b>/b>/p>p>b>/b>/p>h>/h>/dd>/dl>/div>/div>time>/time>tt>/tt>var>/var>div>area>/area>map>/map>bdo>/bdo>div>p>span>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/span>a>span>a>font>/font>/a>a>font>/font>/a>/span>/a>/p>dfn>/dfn>font>/font>ins>/ins>div>/div>/div>/div>script>/script>script>/script>link>link>script>/script>script>/script>script>/script>/body>/html>script>/script>img>`
	e2 := `DOCTYPE>html>head>script>/script>meta>meta>meta>meta>meta>meta>link>title>/title>meta>link>meta>link>meta>meta>script>/script>link>STYLE>/STYLE>script>/script>script>/script>script>/script>script>/script>script>/script>/head>body>div>div>h>img>/h>div>div>div>a>/a>/div>div>a>/a>a>/a>/div>div>strong>/strong>a>/a>a>/a>/div>/div>div>form>input>input>select>option>/option>option>/option>option>/option>/select>input>/form>/div>/div>/div>div>div>/div>div>ul>li>a>span>/span>/a>/li>li>a>span>/span>/a>/li>li>a>span>/span>/a>/li>li>a>span>/span>/a>/li>li>a>span>/span>/a>/li>li>a>span>/span>/a>/li>li>a>span>/span>/a>/li>li>a>span>/span>/a>/li>li>a>span>/span>/a>/li>li>a>span>/span>/a>/li>li>a>span>/span>/a>/li>li>a>span>/span>/a>/li>li>a>span>/span>/a>/li>li>a>span>/span>/a>/li>li>span>/span>/li>li>a>span>/span>/a>/li>/ul>p>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>/p>/div>div>/div>/div>/div>div>img>map>area>area>area>area>area>/map>/div>link>div>div>div>div>div>div>ul>img>/li>li>img>/li>li>img>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>/ul>/div>script>/script>/div>/div>div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>/div>div>ul>li>img>/li>li>a>img>/a>/li>li>img>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>/ul>/div>/div>script>/script>div>div>div>div>h>/h>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>div>div>h>a>/a>/h>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>/div>div>h>a>img>/a>/h>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>/div>/div>div>div>a>img>/a>SCRIPT>div>div>a>img>/a>/div>div>/a>/div>/div>/SCRIPT>SCRIPT>/SCRIPT>/div>ul>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>li>a>img>/a>/li>/ul>/div>div>div>div>div>h>a>/a>/h>/div>div>div>a>img>/a>h>a>/a>/h>p>span>a>/a>/span>/p>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>/div>div>div>h>a>/a>/h>/div>div>div>a>img>/a>h>a>/a>/h>p>span>a>/a>/span>/p>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>/div>/div>div>div>div>h>a>/a>/h>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>div>div>h>a>/a>/h>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>/div>/div>div>script>/script>/div>div>div>div>div>h>a>/a>/h>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>div>div>h>a>/a>/h>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>/div>div>div>div>h>a>/a>/h>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>DIV>/DIV>/ul>/div>div>div>h>a>/a>/h>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>div>DIV>P>/P>/DIV>SCRIPT>li>a>img>/a>span>a>/a>/span>/li>ul>/ul>/SCRIPT>/div>/div>/div>div>script>/script>a>img>/a>/div>div>div>div>div>h>a>/a>/h>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>div>div>h>/h>/div>ul>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>/div>div>div>div>h>a>/a>/h>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>div>div>h>a>/a>/h>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>/div>/div>/div>div>script>/script>/div>div>div>div>div>h>a>/a>/h>/div>ul>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>li>a>/a>/li>/ul>/div>div>ul>li>a>img>/a>span>a>/a>/span>/li>li>a>img>/a>span>a>/a>/span>/li>/ul>/div>/div>div>div>a>img>/a>div>h>a>/a>/h>p>/p>/div>/div>div>div>h>a>/a>/h>/div>ul>li>p>a>/a>/p>span>/span>/li>li>p>a>/a>/p>span>/span>/li>li>p>a>/a>/p>span>/span>/li>li>p>a>/a>/p>span>/span>/li>li>p>a>/a>/p>span>/span>/li>li>p>a>/a>/p>span>/span>/li>li>p>a>/a>/p>span>/span>/li>li>p>a>/a>/p>span>/span>/li>li>p>a>/a>/p>span>/span>/li>/ul>/div>/div>/div>div>div>div>ul>li>a>/a>/li>li>a>/a>/li>/ul>span>a>/a>/span>/div>div>ul>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>li>/li>/ul>/div>div>ul>/ul>/div>/div>div>ul>li>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>/li>li>a>/a>/li>/ul>p>br>span>script>/script>/span>span>script>/script>/span>/p>/div>/div>/html>script>/script>script>/script>script>script>/script>div>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>/div>div>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>a>/a>/div>/body>/html>`
	assert.Equal(t, strconv.FormatInt(int64(int32(sum32([]byte(e2)))), 10), strconv.FormatInt(int64(int32(sum32([]byte(e1)))), 10))
}

/*
cpu: Intel(R) Core(TM) i7-8559U CPU @ 2.70GHz
BenchmarkMurmur3Hash
BenchmarkMurmur3Hash/1
BenchmarkMurmur3Hash/1-8 	10467154	       105.9 ns/op	   9.44 MB/s	      16 B/op	       1 allocs/op
BenchmarkMurmur3Hash/2
BenchmarkMurmur3Hash/2-8 	11458330	       103.5 ns/op	  19.33 MB/s	      16 B/op	       1 allocs/op
BenchmarkMurmur3Hash/4
BenchmarkMurmur3Hash/4-8 	11665981	        97.00 ns/op	  41.24 MB/s	      16 B/op	       1 allocs/op
BenchmarkMurmur3Hash/8
BenchmarkMurmur3Hash/8-8 	10864981	       107.0 ns/op	  74.79 MB/s	      16 B/op	       1 allocs/op
BenchmarkMurmur3Hash/16
BenchmarkMurmur3Hash/16-8         	10371621	       115.4 ns/op	 138.66 MB/s	      16 B/op	       1 allocs/op
BenchmarkMurmur3Hash/32
BenchmarkMurmur3Hash/32-8         	 9674370	       121.1 ns/op	 264.26 MB/s	      16 B/op	       1 allocs/op
BenchmarkMurmur3Hash/64
BenchmarkMurmur3Hash/64-8         	 4910136	       244.8 ns/op	 261.42 MB/s	     144 B/op	       3 allocs/op
BenchmarkMurmur3Hash/128
BenchmarkMurmur3Hash/128-8        	 3711496	       314.9 ns/op	 406.42 MB/s	     272 B/op	       3 allocs/op
BenchmarkMurmur3Hash/256
BenchmarkMurmur3Hash/256-8        	 2461369	       447.3 ns/op	 572.34 MB/s	     528 B/op	       3 allocs/op
BenchmarkMurmur3Hash/512
BenchmarkMurmur3Hash/512-8        	 1679742	       695.8 ns/op	 735.82 MB/s	    1040 B/op	       3 allocs/op
BenchmarkMurmur3Hash/1024
BenchmarkMurmur3Hash/1024-8       	  895694	      1196 ns/op	 855.98 MB/s	    2064 B/op	       3 allocs/op
BenchmarkMurmur3Hash/2048
BenchmarkMurmur3Hash/2048-8       	  513954	      2256 ns/op	 907.78 MB/s	    4112 B/op	       3 allocs/op
BenchmarkMurmur3Hash/4096
BenchmarkMurmur3Hash/4096-8       	  277416	      4200 ns/op	 975.30 MB/s	    8208 B/op	       3 allocs/op
BenchmarkMurmur3Hash/8192
BenchmarkMurmur3Hash/8192-8       	  144415	      8179 ns/op	1001.62 MB/s	   16400 B/op	       3 allocs/op
*/
//func BenchmarkMurmur3Hash32(b *testing.B) {
//	buf := make([]byte, 8192)
//	for length := 1; length <= cap(buf); length *= 2 {
//		b.Run(strconv.Itoa(length), func(b *testing.B) {
//			buf = buf[:length]
//			b.SetBytes(int64(length))
//			b.ReportAllocs()
//			b.ResetTimer()
//			for i := 0; i < b.N; i++ {
//				Murmur3Hash32(string(buf))
//			}
//		})
//	}
//}

/*
cpu: Intel(R) Core(TM) i7-8559U CPU @ 2.70GHz
BenchmarkMurmur3Hash
BenchmarkMurmur3Hash/1
BenchmarkMurmur3Hash/1-8 	 6921716	       166.4 ns/op	   6.01 MB/s	      24 B/op	       1 allocs/op
BenchmarkMurmur3Hash/2
BenchmarkMurmur3Hash/2-8 	 7012362	       167.6 ns/op	  11.93 MB/s	      24 B/op	       1 allocs/op
BenchmarkMurmur3Hash/4
BenchmarkMurmur3Hash/4-8 	 6318135	       183.5 ns/op	  21.80 MB/s	      24 B/op	       1 allocs/op
BenchmarkMurmur3Hash/8
BenchmarkMurmur3Hash/8-8 	 6756994	       174.0 ns/op	  45.97 MB/s	      24 B/op	       1 allocs/op
BenchmarkMurmur3Hash/16
BenchmarkMurmur3Hash/16-8         	 6914974	       171.7 ns/op	  93.17 MB/s	      24 B/op	       1 allocs/op
BenchmarkMurmur3Hash/32
BenchmarkMurmur3Hash/32-8         	 6618970	       175.2 ns/op	 182.67 MB/s	      24 B/op	       1 allocs/op
BenchmarkMurmur3Hash/64
BenchmarkMurmur3Hash/64-8         	 4102794	       283.0 ns/op	 226.16 MB/s	     152 B/op	       3 allocs/op
BenchmarkMurmur3Hash/128
BenchmarkMurmur3Hash/128-8        	 3481050	       332.2 ns/op	 385.36 MB/s	     280 B/op	       3 allocs/op
BenchmarkMurmur3Hash/256
BenchmarkMurmur3Hash/256-8        	 2763493	       419.3 ns/op	 610.48 MB/s	     536 B/op	       3 allocs/op
BenchmarkMurmur3Hash/512
BenchmarkMurmur3Hash/512-8        	 1862610	       620.8 ns/op	 824.75 MB/s	    1048 B/op	       3 allocs/op
BenchmarkMurmur3Hash/1024
BenchmarkMurmur3Hash/1024-8       	 1221684	       967.4 ns/op	1058.48 MB/s	    2072 B/op	       3 allocs/op
BenchmarkMurmur3Hash/2048
BenchmarkMurmur3Hash/2048-8       	  709380	      1730 ns/op	1183.65 MB/s	    4120 B/op	       3 allocs/op
BenchmarkMurmur3Hash/4096
BenchmarkMurmur3Hash/4096-8       	  389712	      3051 ns/op	1342.41 MB/s	    8216 B/op	       3 allocs/op
BenchmarkMurmur3Hash/8192
BenchmarkMurmur3Hash/8192-8       	  204080	      5738 ns/op	1427.63 MB/s	   16408 B/op	       3 allocs/op
*/
func BenchmarkMurmur3Hash(b *testing.B) {
	buf := make([]byte, 8192)
	for length := 1; length <= cap(buf); length *= 2 {
		b.Run(strconv.Itoa(length), func(b *testing.B) {
			buf = buf[:length]
			b.SetBytes(int64(length))
			b.ReportAllocs()
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				Murmur3Hash(string(buf))
			}
		})
	}
}

/*
cpu: Intel(R) Core(TM) i7-8559U CPU @ 2.70GHz
BenchmarkMD5Hash
BenchmarkMD5Hash/1
BenchmarkMD5Hash/1-8 	 3862292	       308.1 ns/op	   3.25 MB/s	      64 B/op	       2 allocs/op
BenchmarkMD5Hash/2
BenchmarkMD5Hash/2-8 	 3646572	       328.9 ns/op	   6.08 MB/s	      66 B/op	       3 allocs/op
BenchmarkMD5Hash/4
BenchmarkMD5Hash/4-8 	 3406857	       327.7 ns/op	  12.21 MB/s	      68 B/op	       3 allocs/op
BenchmarkMD5Hash/8
BenchmarkMD5Hash/8-8 	 3590778	       357.2 ns/op	  22.40 MB/s	      72 B/op	       3 allocs/op
BenchmarkMD5Hash/16
BenchmarkMD5Hash/16-8         	 3517429	       340.6 ns/op	  46.98 MB/s	      80 B/op	       3 allocs/op
BenchmarkMD5Hash/32
BenchmarkMD5Hash/32-8         	 3148732	       342.3 ns/op	  93.49 MB/s	      96 B/op	       3 allocs/op
BenchmarkMD5Hash/64
BenchmarkMD5Hash/64-8         	 2538495	       471.8 ns/op	 135.64 MB/s	     192 B/op	       4 allocs/op
BenchmarkMD5Hash/128
BenchmarkMD5Hash/128-8        	 2059492	       584.5 ns/op	 219.00 MB/s	     320 B/op	       4 allocs/op
BenchmarkMD5Hash/256
BenchmarkMD5Hash/256-8        	 1520538	       797.1 ns/op	 321.16 MB/s	     576 B/op	       4 allocs/op
BenchmarkMD5Hash/512
BenchmarkMD5Hash/512-8        	  976092	      1236 ns/op	 414.25 MB/s	    1088 B/op	       4 allocs/op
BenchmarkMD5Hash/1024
BenchmarkMD5Hash/1024-8       	  573176	      2119 ns/op	 483.33 MB/s	    2112 B/op	       4 allocs/op
BenchmarkMD5Hash/2048
BenchmarkMD5Hash/2048-8       	  274507	      3760 ns/op	 544.74 MB/s	    4160 B/op	       4 allocs/op
BenchmarkMD5Hash/4096
BenchmarkMD5Hash/4096-8       	  150322	      6992 ns/op	 585.77 MB/s	    8256 B/op	       4 allocs/op
BenchmarkMD5Hash/8192
BenchmarkMD5Hash/8192-8       	   80367	     13833 ns/op	 592.19 MB/s	   16448 B/op	       4 allocs/op

*/
func BenchmarkMD5Hash(b *testing.B) {
	f := func(a string) string {
		d := md5.Sum([]byte(a))
		return hex.EncodeToString(d[:])
	}
	buf := make([]byte, 8192)
	for length := 1; length <= cap(buf); length *= 2 {
		b.Run(strconv.Itoa(length), func(b *testing.B) {
			buf = buf[:length]
			b.SetBytes(int64(length))
			b.ReportAllocs()
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				f(string(buf))
			}
		})
	}
}
