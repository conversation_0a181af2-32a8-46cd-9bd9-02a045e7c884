package algo

import "bytes"

var (
	defaultMagicKey = "sutra"
	MagicKey        string
)

// InitKey 初始化key，可以不调用，就会用默认的值
// 不足8字节会以8字节对齐
func InitKey(key string) string {
	if len(key) > 0 {
		MagicKey = key
	} else {
		MagicKey = defaultMagicKey
	}

	// 8字节对齐
	if m := len(MagicKey) % 8; m != 0 {
		padText := bytes.Repeat([]byte{' '}, 8-m)
		MagicKey += string(padText)
	}
	return MagicKey
}

func init() {
	InitKey("")
}
