# This file is a template, and might need editing before it works on your project.
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Go.gitlab-ci.yml

image: ccr.ccs.tencentyun.com/pixiu/golang-base:1.18.2-pcap

variables:
  # Please edit to your GitLab project
  REPO_NAME: git.gobies.org/sutra/gosutra
  GIT_SSL_NO_VERIFY: "true" #关闭git的https签名验证

# The problem is that to be able to use go get, one needs to put
# the repository in the $GOPATH. So for example if your gitlab domain
# is gitlab.com, and that your repository is namespace/project, and
# the default GOPATH being /go, then you'd need to have your
# repository in /go/src/gitlab.com/namespace/project
# Thus, making a symbolic link corrects this.
before_script:
  - mkdir -p `go env GOPATH`/src/$(dirname $REPO_NAME)
  - ln -svf $CI_PROJECT_DIR `go env GOPATH`/src/$REPO_NAME
  - cd `go env GOPATH`/src/$REPO_NAME
  - go env -w 'GOPRIVATE=*.gobies.org'
  - go env -w GOPROXY="https://goproxy.cn,direct"
  - go get -t # 考虑测试库
#  - go mod tidy # 否则会报cmd下的mod错误
  - go mod download
  - touch resources/rules.json # 解决规则缺失的报错问题

stages:
  - test
  - build

coverage:
  stage: test
  script:
    - CGO_ENABLED=1 go test ./... -cover -coverprofile=cover.out
    - CGO_ENABLED=1 go tool cover --func=cover.out # 打印字符串用于提取covrage的badge：total:\s+\(statements\)\s+(\d+.\d+\%)
    - CGO_ENABLED=1 go tool cover -html=cover.out -o index.html
  artifacts:
    paths: 
      - index.html

compile:
  stage: build
  script:
    - go build
