package fofa

import (
	"fmt"
	"os"
)

func GenFofaUrlFromEnvi() string {
	url := "https://fofa.so"
	if v := os.Getenv("FOFA_API_SERVER"); len(v) > 0 {
		url = v
	} else if v := os.Getenv("FOFA_SERVER"); len(v) > 0 {
		url = v
	}
	email := ""
	if v := os.Getenv("FOFA_EMAIL"); len(v) > 0 {
		email = v
	}
	key := ""
	if v := os.Getenv("FOFA_KEY"); len(v) > 0 {
		key = v
	}
	version := "v1"
	if v := os.Getenv("FOFA_VERSION"); len(v) > 0 {
		version = v
	}

	full := ""
	if v := os.Getenv("FOFA_OPTION_FULL"); len(v) > 0 {
		full = "&full=true"
	}
	tlsdisabled := ""
	if v := os.Getenv("FOFA_OPTION_TLS_DISABLE"); len(v) > 0 {
		tlsdisabled = "&tlsdisabled=false"
	}
	debugLevel := ""
	if v := os.Getenv("FOFA_OPTION_DEBUG_LEVEL"); len(v) > 0 {
		debugLevel = "&debuglevel=" + v
	}
	fofaurl := fmt.Sprintf("%s/?email=%s&key=%s&version=%s%s%s%s",
		url, email, key, version, debugLevel, tlsdisabled, full)
	if v := os.Getenv("FOFA_CLIENT_URL"); len(v) > 0 {
		fofaurl = v
	}

	return fofaurl
}
