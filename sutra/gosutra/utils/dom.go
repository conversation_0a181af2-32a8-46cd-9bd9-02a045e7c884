/*
Package utils 提供了支撑的函数

通过 BodyHash2 来生成body中的html归一化的hash，和完整内容的hash:
	utils.BodyHash2("abc") // 第一个返回是归一化的hash， 第二个返回是html的hash
*/
package utils

import (
	"git.gobies.org/sutra/gosutra/algo"
	"regexp"
)

var hrefRegexp = regexp.MustCompile("<[?!]?(/?[a-zA-Z]+) ?.*?>")

// htmlNormalizeWithCountLength
// 第一个参数是归一化的值，第二个参数是归一化以后的长度，第三个参数是处理的个数
func htmlNormalizeWithCountLength(html string) (string, int, int) {
	domTree := ""
	count := 0
	match := hrefRegexp.FindAllStringSubmatch(html, -1)
	if match != nil {
		for _, v := range match {
			domTree = domTree + v[1] + ">"
			count++
		}
	}
	return domTree, len(domTree), count
}

// HTMLNormalize 根据html内容生成归一化的字符串
func HTMLNormalize(html string) string {
	domTree, _, _ := htmlNormalizeWithCountLength(html)
	return domTree
}

/*
BodyNormalizeHash 根据html生成归一化的hash
*/
func BodyNormalizeHash(html string) string {
	// 归一化
	dom := HTMLNormalize(html)
	return algo.Murmur3Hash(dom)
}

/*
BodyHash2 根据html生成两个字符串：归一化的nhash，以及完整内容的fhash
*/
func BodyHash2(html string) (string, string) {
	nhash, fhash, _, _ := BodyHash2WithNormalizeLengthAndCount(html)
	return nhash, fhash
}

/*
BodyHash2WithNormalizeCountAndLength 根据html生成两个字符串以及两个数字：
归一化的nhash，以及完整内容的fhash，归一化以后的长度，和归一化处理的个数
*/
func BodyHash2WithNormalizeLengthAndCount(html string) (string, string, int, int) {
	dom, length, count := htmlNormalizeWithCountLength(html)
	return algo.Murmur3Hash(dom), algo.Murmur3Hash(html), length, count
}
