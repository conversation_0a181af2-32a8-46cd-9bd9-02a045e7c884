/*
sutradump 完成导出所有fofa的发布规则列表，并且分析全网的数据量
*/
package main

import (
	"database/sql"
	"encoding/csv"
	"flag"
	"fmt"
	"git.gobies.org/sutra/gosutra/fofa"
	"git.gobies.org/sutra/gosutra/rulengine"
	_ "github.com/go-sql-driver/mysql"
	"html/template"
	"io"
	"log"
	"os"
	"strconv"
	"strings"
	"time"
)

var (
	dbdsn        string // 数据库连接字符串
	outFile      string // 导出文件
	fofaUri      string // fofa服务器地址
	dumpFofaSize bool   // 是否dumpfofasize
	checkAppStat bool   // 是否checkAppStat
	dumpSize     int
	queryFilter  string
)

type Rule struct {
	ID          string
	Product     string
	Content     string
	Fields      []string
	FofaSize    int32
	IsSubdomain bool //是否只有subdomain
	OK          bool
	HashNum     int
	Reason      string
}

func CheckErr(err error) {
	if err != nil {
		panic(err)
	}
}

// https://play.golang.org/p/Qg_uv_inCek
// contains checks if a string is present in a slice
func contains(s []string, str string) bool {
	for _, v := range s {
		if v == str {
			return true
		}
	}

	return false
}

type FidStat struct {
	Fid      string
	AppCount int // app下的数量
	AllCount int // fofa的总数
	Diff     int // 相差的百分比
}

type CheckRecorder struct {
	Ok       bool   // 是否可以上云
	Reason   string //失败的原因
	IPNUm    int
	HashNum  int `json:"hash_num"`
	FidStats []FidStat
}

// appStat 判断app能否生成hash上云端
func appStat(fofacli *fofa.Fofa, name string) (cr CheckRecorder) {
	// 提取所有的fid以及数量
	var r *fofa.FofaApiStats
	var err error
	for {
		r, err = fofacli.FIDStats(`app="`+name+`"`, 0)
		if err == nil {
			break
		} else {
			log.Println("[ERROR] fofa stats failed of :", name)
			time.Sleep(time.Second * 1)
		}
	}

	cr.IPNUm = r.IPNUm
	cr.HashNum = r.HashNum

	if r.HashNum == 0 || r.IPNUm == 0 {
		return
	}

	// 总数超过10万的不跑
	if r.IPNUm > 100000 {
		cr.Reason = "records more than 100000"
		return
	}
	// fid超过100的不跑
	if r.HashNum > 100 {
		cr.Reason = "fids more than 100"
		return
	}

	i := 0
	for _, fidinfo := range r.Result.List.FIDs {
		i++
		if i > 5 {
			break
		}
		size := int(fofacli.FetchSize(`fid="` + fidinfo.Name + `"`))
		fs := FidStat{
			Fid:      fidinfo.Name,
			AppCount: fidinfo.Count,
			AllCount: size,
			Diff:     100 * (size - fidinfo.Count) / fidinfo.Count,
		}
		cr.FidStats = append(cr.FidStats, fs)
		if fs.Diff < 2 {
			cr.Ok = true
		}
	}

	if !cr.Ok {
		cr.Reason = "fid count failed"
	}

	// 每个fid查询整个fofa
	// 如果相差小于5%，认为是可以的
	return
}

func main() {
	// 解析参数
	flag.StringVar(&dbdsn, "dsn", "root:@/fofapro", "db connection string")
	flag.StringVar(&outFile, "outFile", "rulesout.html", "rules file to output")
	flag.StringVar(&fofaUri, "fofaUri", "https://fofapro.com/?email="+os.Getenv("FOFA_EMAIL")+"&key="+os.Getenv("FOFA_KEY")+"&version=v1&debuglevel=0", "fofa server")
	flag.BoolVar(&dumpFofaSize, "dumpFofaSize", false, "whether dump fofa size")
	flag.BoolVar(&checkAppStat, "checkAppStat", false, "whether dump fofa size")
	flag.IntVar(&dumpSize, "dumpSize", -1, "dumpSize")
	flag.StringVar(&queryFilter, "queryFilter", "", "queryFilter")
	flag.Parse()

	filter := ""
	if dumpSize > 0 {
		filter = fmt.Sprintf(" limit %d ", dumpSize)
	}
	// 读取数据库
	db, err := sql.Open("mysql", dbdsn)
	CheckErr(err)
	rows, err := db.Query(`select id,product,rule from rules where sutra=1 ` + queryFilter + ` order by id ` + filter)
	CheckErr(err)
	defer rows.Close()

	// 链接fofa
	fofacli := fofa.NewFofaCli(fofaUri)

	subdomainSize := 0
	var rules []*Rule
	for rows.Next() {
		var id, product, content string
		err = rows.Scan(&id, &product, &content)
		CheckErr(err)

		//log.Println("process db id:", id)

		r, err := rulengine.NewRule(product, content)
		CheckErr(err)
		if r.IsSubdomain() {
			subdomainSize++
		}

		tmpRule := Rule{
			ID:          id,
			Product:     product,
			Content:     content,
			Fields:      r.Fields,
			IsSubdomain: r.IsSubdomain(),
		}
		//log.Println(id, product, content)
		rules = append(rules, &tmpRule)

	}

	// 写csv
	file, err := os.Create("records.csv")
	defer file.Close()
	if err != nil {
		log.Fatalln("failed to open file", err)
	}

	csvwriter := csv.NewWriter(file)
	defer csvwriter.Flush()

	err = csvwriter.Write([]string{"id", "product", "content", "fileds", "subdomain", "fofasize", "fidsize", "ok", "reason"})
	CheckErr(err)
	for _, rule := range rules {
		if dumpFofaSize {
			rule.FofaSize = fofacli.FetchSize(rule.Content)
		}
		if checkAppStat && rule.IsSubdomain {
			cr := appStat(fofacli, rule.Product)
			CheckErr(err)
			rule.OK = cr.Ok
			rule.HashNum = cr.HashNum
			rule.FofaSize = int32(cr.IPNUm)
			rule.Reason = cr.Reason
		}
		record := []string{
			rule.ID,
			rule.Product,
			rule.Content,
			strings.Join(rule.Fields, "|"),
			strconv.FormatBool(rule.IsSubdomain),
			strconv.Itoa(int(rule.FofaSize)),
			strconv.Itoa(int(rule.HashNum)),
			strconv.FormatBool(rule.OK),
			rule.Reason,
		}
		err = csvwriter.Write(record)
		CheckErr(err)
		csvwriter.Flush()
		log.Println(record)
	}

	//生成html文件
	tmpl := template.Must(template.ParseFiles("rules.html"))
	var w io.Writer
	if outFile == "-" {
		w = os.Stdout
	} else {
		w, err = os.OpenFile(outFile, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0600)
		CheckErr(err)
	}

	err = tmpl.Execute(w, map[string]interface{}{
		"Rules":           rules,
		"AllSize":         len(rules),
		"IsSubdomainSize": subdomainSize,
	})

	log.Println("ok")
}
