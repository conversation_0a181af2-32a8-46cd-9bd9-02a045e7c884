/*
genhash 通过规则生成hash
*/
package main

import (
	"bufio"
	"bytes"
	"encoding/gob"
	"encoding/json"
	"flag"
	"fmt"
	"git.gobies.org/sutra/gosutra/algo"
	"git.gobies.org/sutra/gosutra/fofa"
	"log"
	"os"
	"strconv"
	"time"
)

var (
	fofaUrl    string
	fidMinSize int
)

func copyMap(in, out interface{}) {
	buf := new(bytes.Buffer)
	gob.NewEncoder(buf).Encode(in)
	gob.NewDecoder(buf).Decode(out)
}

func checkrule(fofacli *fofa.Fofa, jsonrule string) {
	var ruleObj map[string]interface{}
	if err := json.Unmarshal([]byte(jsonrule), &ruleObj); err != nil {
		log.Println("[ERROR]", err)
		return
	}
	if _, ok := ruleObj["rule"]; !ok {
		log.Println("[ERROR] rule json format failed")
		return
	}
	if _, ok := ruleObj["product"]; !ok {
		log.Println("[ERROR] rule json format failed")
		return
	}
	if ruleObj["level"].(string) != "1" && ruleObj["level"].(string) != "5" {
		log.Println("[WARNING] no need to export, level:", ruleObj["level"].(string))
		return
	}

	log.Println(" -- process product:", ruleObj["product"].(string))

	// 查询fofa，提取fid
	var r *fofa.FofaApiStats
	var err error
	for {
		r, err = fofacli.FIDStats(ruleObj["rule"].(string), 0)
		if err == nil {
			break
		} else {
			log.Println("[ERROR] fofa stats failed of :", ruleObj["product"].(string))
			time.Sleep(time.Second * 1)
		}
	}

	// 确认fid对应在app的数量，以及在fofa的总数
	for _, fidinfo := range r.Result.List.FIDs {
		log.Println(" -- process fid:", fidinfo.Name, "product:", ruleObj["product"].(string))
		nhash, err := algo.DecryptFromBase64(fidinfo.Name, "fofafofa")
		if err != nil {
			log.Println("[ERROR] base64 decode fid failed :", fidinfo.Name, err)
			continue
		}

		_, err = strconv.Atoi(nhash)
		if err != nil {
			log.Println("[ERROR] nhash is not int64 :", fidinfo.Name, err)
			continue
		}

		// 如果完全一样，就生成规则，入db库
		fidSize := int(fofacli.FetchSize(`fid="` + fidinfo.Name + `"`))
		if fidSize == fidinfo.Count && fidSize > fidMinSize {
			ehash := algo.SignString(nhash)
			var tmphashobj map[string]interface{}
			copyMap(&ruleObj, &tmphashobj)
			tmphashobj["ehash"] = ehash
			tmphashobj["nhash"] = nhash
			delete(tmphashobj, "rule")

			d, err := json.Marshal(tmphashobj)
			if err != nil {
				log.Println("[ERROR] json encode fid failed :", fidinfo.Name, err)
				continue
			}
			fmt.Println(string(d))
		}
	}
}

func main() {
	flag.StringVar(&fofaUrl, "fofaUrl", "http://172.16.40.24:8082/?email=<EMAIL>&key=583aba2991abbd5a9b2a731a8d0af907&version=v2&tlsdisabled=false", "<url>/?email=<email>&key=<key>&version=<version>")
	flag.IntVar(&fidMinSize, "fidMinSize", 5, "fid min size")
	flag.Parse()

	fofacli := fofa.NewFofaCli(fofaUrl)
	log.Println("check fofa service status...")
	size := fofacli.FetchSize("type=subdomain")
	log.Println("fofa subdomain size:", size)
	if size == 0 {
		panic("fofa is broken")
	}

	scanner := bufio.NewScanner(os.Stdin)
	scanner.Buffer([]byte{}, bufio.MaxScanTokenSize*1024)
	for scanner.Scan() { // internally, it advances token based on sperator
		line := scanner.Text()
		if len(line) > 0 {
			checkrule(fofacli, line)
		}
	}

	if err := scanner.Err(); err != nil {
		fmt.Println(err)
	}
}
