/*
根据用户的输入规则，生成原始json数据

	../queryjson/queryjson -fieldStr "host,fidv2,ip,port,lastupdatetime" -filter 'type=subdomain && ip="************/24" && fidv2!=""' -dumpSize 1 | jq
	{
	  "fidv2": "ZvJCtGzrqEyHLFflRL595vwXT9k+bh/O",
	  "host": "https://**************",
	  "ip": "**************",
	  "lastupdatetime": "2022-02-28 11:16:45",
	  "port": "443",
	  "query": "type=subdomain && ip=\"************/24\" && fidv2!=\"\""
	}


	echo -e '{"fofa_query":"ip=\"************/24\""}' | ../queryjson/queryjson -mode size | jq
	{
	  "fofa_query": "ip=\"************/24\"",
	  "fofa_size": 780575
	}

*/
package main

import (
	"bufio"
	"encoding/json"
	"flag"
	"fmt"
	"git.gobies.org/sutra/gosutra/fofa"
	"io"
	"log"
	"os"
	"strings"
)

var (
	fofaUrl  string
	filter   string
	dumpSize int
	fieldStr string
	mode     string
	fofacli  *fofa.Fofa
)

func processRecord(fields []string, items *[]string) {
	m := make(map[string]string)
	m["query"] = filter
	i := 0
	for _, f := range fields {
		m[f] = (*items)[i]
		i++
	}
	d, err := json.Marshal(m)
	if err != nil {
		log.Println("[ERROR]", err)
	}
	fmt.Println(string(d))
}

func flagParse() {
	flag.StringVar(&filter, "filter", "", "fofa raw query")
	flag.StringVar(&fofaUrl, "fofaUrl", fofa.GenFofaUrlFromEnvi(), "<url>/?email=<email>&key=<key>&version=<v2>&tlsdisabled=<false>&debuglevel=<0>&full=<true>")
	flag.IntVar(&dumpSize, "dumpSize", 10, "dump size, -1 mean unlimit")
	flag.StringVar(&fieldStr, "fieldStr", "host,fidv2,lastupdatetime", "split with ','")
	flag.StringVar(&mode, "mode", "fetch", "available: fetch/size")
	flag.Parse()
}

func main() {
	flagParse()

	fofacli = fofa.NewFofaCli(fofaUrl)
	switch mode {
	case "fetch":
		if len(filter) == 0 {
			log.Println("no fofa query, please use 'filter' keyword")
			return
		}
		allFields := strings.Split(fieldStr, ",")
		fofacli.Fetch(filter, fieldStr, dumpSize, func(fields []string, allSize int32) bool {
			processRecord(allFields, &fields)
			return true
		})
	case "size":
		processInput(os.Stdin, processLine)
	}

}

func processInput(reader io.Reader, check func(line string) bool) bool {
	scanner := bufio.NewScanner(reader)
	scanner.Buffer([]byte{}, bufio.MaxScanTokenSize*1024)
	for scanner.Scan() { // internally, it advances token based on sperator
		line := scanner.Text()
		if len(line) > 0 {
			check(line)
		}
	}

	if err := scanner.Err(); err != nil {
		log.Println(err)
		return false
	}

	return true
}

// 每一行是一个json
func processLine(line string) bool {
	var m map[string]interface{}
	if err := json.Unmarshal([]byte(line), &m); err != nil {
		log.Println("[ERROR]", err)
		return false
	}

	var size int32 = 0
	var records [][]string
	fofacli.Fetch(m["fofa_query"].(string), fieldStr, dumpSize, func(fields []string, allSize int32) bool {
		size = allSize
		records = append(records, fields)
		return true
	})
	m["fofa_size"] = size
	m["fofa_records"] = records

	if b, err := json.Marshal(m); err != nil {
		log.Println("[ERROR]", err)
		return false
	} else {
		fmt.Println(string(b))
	}
	return true
}
