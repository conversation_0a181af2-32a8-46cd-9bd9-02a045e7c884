/*
通过配置文件的id列表，导出规则的json文件
*/
package main

import (
	"bufio"
	"database/sql"
	"encoding/json"
	"flag"
	"git.gobies.org/sutra/gosutra/structs"
	_ "github.com/go-sql-driver/mysql"
	"github.com/vmihailenco/msgpack/v5"
	"os"
)

var (
	dbdsn     string // 数据库连接字符串
	outFile   string // 导出文件
	inFile    string // id文件
	isEncrypt bool   // 是否加密
)

func CheckErr(err error) {
	if err != nil {
		panic(err)
	}
}

func fillRule(rows *sql.Rows) *structs.Product {
	ri := &structs.Product{}
	var softhard interface{}
	var level interface{}
	var enProduct interface{}
	var enComopany interface{}
	var enCategory interface{}
	var enParentCategory interface{}
	var category interface{}
	var parentCategory interface{}
	err := rows.Scan(&ri.RuleId, &ri.Rule, &softhard, &level, &ri.Name, &ri.Company, &enComopany, &enProduct, &category, &parentCategory, &enCategory, &enParentCategory)
	CheckErr(err)

	//if softhard != nil && softhard.(int64) > 0 {
	//	ri.SoftHard = strconv.Itoa(int(softhard.(int64)))
	//}
	//if level != nil && level.(int64) > 0 {
	//	ri.Level = strconv.Itoa(int(level.(int64)))
	//}
	if softhard != nil && len(softhard.([]byte)) > 0 {
		ri.SoftHard = string(softhard.([]byte))
	}
	if level != nil && len(level.([]byte)) > 0 {
		ri.Level = string(level.([]byte))
	}

	if category != nil && len(category.([]byte)) > 0 {
		ri.Category = string(category.([]byte))
	}
	if parentCategory != nil && len(parentCategory.([]byte)) > 0 {
		ri.ParentCategory = string(parentCategory.([]byte))
	}

	//如果有英文则优先取英文
	if enProduct != nil && len(enProduct.([]byte)) > 0 {
		ri.Name = string(enProduct.([]byte))
	}
	if enComopany != nil && len(enComopany.([]byte)) > 0 {
		ri.Company = string(enComopany.([]byte))
	}
	if enCategory != nil && len(enCategory.([]byte)) > 0 {
		ri.Category = string(enCategory.([]byte))
	}
	if enParentCategory != nil && len(enParentCategory.([]byte)) > 0 {
		ri.ParentCategory = string(enParentCategory.([]byte))
	}
	return ri
}

func main() {
	// 解析参数
	flag.StringVar(&dbdsn, "dsn", "root:@/fofapro", "db conennction string")
	flag.StringVar(&inFile, "inFile", "", "rules file to output")
	flag.StringVar(&outFile, "outFile", "rules.json", "rules file to output")
	flag.BoolVar(&isEncrypt, "isEncrypt", false, "is encrypt rules")
	flag.Parse()

	// 读取数据库
	db, err := sql.Open("mysql", dbdsn)
	CheckErr(err)
	baseSql := `select r.id, r.rule, r.soft_hard_code, r.level_code, r.product, r.company, r.en_company, r.en_product, sc.category, sc.parent_category, sc.en_category, sc.en_parent_category from rules r 
left join second_category_rules scr on r.id=scr.rule_id 
left join (select a.id as cid, a.title as category, a.en_title as en_category, (select b.title from second_categories b where a.ancestry = b.id) parent_category, (select b.en_title from second_categories b where a.ancestry = b.id) en_parent_category
 from second_categories a) sc on scr.second_category_id=sc.cid`

	// 生成目标文件：rules.json
	attributesFile, err := os.Create(outFile)
	if err != nil {
		panic(err)
	}
	defer attributesFile.Close()

	// 导出数据
	var res []*structs.Product

	// 加载id文件
	if len(inFile) > 0 {
		idf, err := os.Open(inFile)
		if err != nil {
			panic(err)
		}
		defer idf.Close()

		scanner := bufio.NewScanner(idf)
		for scanner.Scan() { // internally, it advances token based on sperator
			line := scanner.Text()
			if len(line) > 0 {
				rows, err := db.Query(baseSql+` where r.garbage_rule=0 and r.id=?`, line)

				CheckErr(err)
				defer rows.Close()

				for rows.Next() {
					ri := fillRule(rows)
					res = append(res, ri)
				}
			}
		}
	} else {
		rows, err := db.Query(baseSql + ` where r.published=1`)

		CheckErr(err)
		defer rows.Close()

		for rows.Next() {
			ri := fillRule(rows)
			res = append(res, ri)
		}
	}

	if isEncrypt {
		for _, r := range res {
			data, err := msgpack.Marshal(r)
			CheckErr(err)

			attributesFile.Write(data)
			attributesFile.WriteString("\r\n")
		}

		return
	}

	for _, r := range res {
		data, err := json.Marshal(r)
		CheckErr(err)

		attributesFile.Write(data)
		attributesFile.WriteString("\r\n")
	}
}
