// ==========================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package service

import (
	"context"

	"git.gobies.org/sutra/gosutra/structs"
)

type IRule interface {
	Match(ctx context.Context, data string) ([]*structs.Product, error)
	Add(ctx context.Context, data string) int
	Reset(ctx context.Context, data string) int
}

var localRule IRule

func Rule() IRule {
	if localRule == nil {
		panic("implement not found for interface IRule, forgot register?")
	}
	return localRule
}

func RegisterRule(i IRule) {
	localRule = i
}
