package rule

import (
	"fmt"
	"testing"

	"git.gobies.org/sutra/gosutra"
	"git.gobies.org/sutra/gosutra/rulengine"
	"github.com/gogf/gf/v2/container/gset"
	"github.com/gogf/gf/v2/os/gctx"
)

func Test_sRule_Match(t *testing.T) {
	ctx := gctx.New()
	re := New()
	re.Add(ctx, `
{"product":"iKuai-Cloud","rule":"body=\"\u003cstrong\u003eWe're sorry but iKuai Cloud Platform doesn't \"","rule_id":"669499","level":"5","category":"Virtualization","parent_category":"Support System","softhard":"2","company":"Quanxun Convergence Network Technology (Beijing) Co., Ltd.","from":""}
{"product":"Comtrend-Gigabit-802.11n-Router","rule":"header=\"realm=\\\"Comtrend Gigabit 802.11n Router\" || banner=\"Comtrend Gigabit 802.11n Router\"","rule_id":"72659","level":"1","category":"Router","parent_category":"Network Device","softhard":"1","company":"Comtrend Corporation","from":""}
{"product":"sky-Router","rule":"header=\"realm=\\\"SKY Router\" || banner=\"realm=\\\"SKY Router\" || (banner=\"Server: sky_router\" \u0026\u0026 banner!=\"couchdb\" \u0026\u0026 banner!=\"drupal\") || (header=\"Server: sky_router\" \u0026\u0026 header!=\"couchdb\" \u0026\u0026 header!=\"drupal\")","rule_id":"75743","level":"1","category":"Router","parent_category":"Network Device","softhard":"1","company":"Sky UK","from":""}
{"product":"D-link-DSL-2640B","rule":"body=\"Product : DSL-2640B\" || title=\"D-Link DSL-2640B\"","rule_id":"20355","level":"1","category":"Router","parent_category":"Network Device","softhard":"1","company":"Youxun Technology Co., Ltd.","from":""}
{"product":"IBM-WebSphere-Application-Server","rule":"((header=\"Server: WebSphere Application Server\" || title=\"IBM WebSphere Application Server\") \u0026\u0026 header!=\"couchdb\" \u0026\u0026 header!=\"drupal\") || (banner=\"Server: WebSphere Application Server\" \u0026\u0026 banner!=\"couchdb\" \u0026\u0026 banner!=\"drupal\")","rule_id":"662917","level":"3","category":"Other Enterprise Application","parent_category":"Enterprise Application","softhard":"2","company":"International Business Machines Corporation","from":""}
{"product":"And Tong intelligent","rule":"body=\"href=\\\"/web/mainMenu/images/favicon.ico\\\"\u003e\"","rule_id":"666431","level":"5","category":"Other Software System","parent_category":"Software System","softhard":"2","company":"Zhongshan Hetong Intelligent Technology Co., Ltd.","from":""}
{"product":"Kedacom-NVR","rule":"title=\"NVR Station Web\" || (body=\"location=\\\"index_cn.htm\\\";\" \u0026\u0026 body=\"if(syslan == \\\"zh-cn\") || title=\"WMS browse NVR\"","rule_id":"9450","level":"1","category":"Other IoT Device","parent_category":"IoT Device","softhard":"1","company":"Suzhou Keda Technology Co., Ltd.","from":""}
  {"product": "YXLINK-scanner", "rule": "body=\"content=\\\"铱迅漏洞扫描系统\" || title=\"yXLINK\" || cert=\"cn=nvs.yxlink.com\"", "rule_id": "9381", "level": "1", "category": "Vulnerability Scanning", "parent_category": "Network Security", "softhard": "1", "company": "Nanjing Yixun Information Technology Co., Ltd.", "from": ""}
`)

	type fields struct {
		sutra *gosutra.Client
	}
	type args struct {
		data string
	}
	tests := []struct {
		name      string
		fields    fields
		args      args
		wantRules *gset.StrSet
		wantErr   bool
	}{
		{
			args: args{
				data: "{\"title\": \"Welcome to Management System\", \"body\": \"welcome login\"}",
			},
		},
		{
			args: args{
				data: `

{"title": "D-Link DSL-2640B",
"body": "dlink",
"ip": "*******"}
`,
			},
			wantRules: gset.NewStrSetFrom([]string{"D-link-DSL-2640B"}),
		},
		{
			args: args{
				data: `
{
    "domain": "<EMAIL>",
    "title": "yXLINK",
    "body": "sfeef铱迅漏洞扫描系统aefef",
    "header": "commodo elit nulla",
    "server": "commodo"
}
`,
			},
			wantRules: gset.NewStrSetFrom([]string{"YXLINK-scanner"}),
		},
	}
	fmt.Println("rules: ", rulengine.RuleSize())
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := re.Match(ctx, tt.args.data)
			if (err != nil) != tt.wantErr {
				t.Errorf("sRule.Match() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantRules != nil {
				getRules := gset.NewStrSet()
				for _, r1 := range got {
					getRules.Add(r1.Name)
				}
				if !getRules.Equal(tt.wantRules) {
					t.Errorf("sRule.Match() = %v, want %v", got, tt.wantRules)
				}

			}
		})
	}
}
