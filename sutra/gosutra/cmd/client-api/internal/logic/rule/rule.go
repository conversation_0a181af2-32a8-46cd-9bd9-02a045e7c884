package rule

import (
	"client-api/internal/service"
	"context"
	_ "embed"
	"git.gobies.org/sutra/gosutra/rulengine/rsa"
	"log"

	"git.gobies.org/sutra/gosutra"
	"git.gobies.org/sutra/gosutra/rulengine"
	"git.gobies.org/sutra/gosutra/structs"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/net/gtrace"
)

type sRule struct {
	sutra     *gosutra.Client
	isEncrypt bool
}

//go:embed pub.pem
var PublicKey []byte

func init() {
	service.RegisterRule(New())
}

func New() *sRule {
	var client *gosutra.Client

	if len(PublicKey) > 0 {
		err := rsa.RSA.SetPublicKey(string(PublicKey))
		if err != nil {
			log.Fatalln(err, "load rsa public key failed")
		}

		client = newSutraClientForEncrypt("")
	} else {
		client = newSutraClient("")
	}

	return &sRule{sutra: client}
}

func newSutraClientForEncrypt(rules string) *gosutra.Client {
	gosutra.LoadInnerEncryptRules()
	args := []gosutra.ClientOption{
		gosutra.WithCloudQuery(false),
		gosutra.WithCloseLocalEngine(false),
		gosutra.WithQueryRawJson(false),
		gosutra.WithDebug(true),
	}
	if rules != "" {
		args = append(args, gosutra.WithRules(context.Background(), rules, true))
	}

	return gosutra.NewClient(args...)
}

func newSutraClient(rules string) *gosutra.Client {
	gosutra.LoadInnerRules()
	args := []gosutra.ClientOption{
		gosutra.WithCloudQuery(false),
		gosutra.WithCloseLocalEngine(false),
		gosutra.WithQueryRawJson(false),
		gosutra.WithDebug(true),
	}
	if rules != "" {
		args = append(args, gosutra.WithRules(context.Background(), rules, true))
	}

	return gosutra.NewClient(args...)
}

func (r *sRule) Match(ctx context.Context, data string) ([]*structs.Product, error) {
	ctx, span := gtrace.NewSpan(ctx, "Rule.Match")
	defer span.End()
	res, err := r.sutra.Products(data)
	gjson.New(res).Dump()
	return res, err
}

func (r *sRule) Add(ctx context.Context, data string) int {
	ctx, span := gtrace.NewSpan(ctx, "Rule.Add")
	defer span.End()

	rulengine.Load(ctx, data, false)
	return rulengine.RuleSize()
}

func (r *sRule) Reset(ctx context.Context, data string) int {
	ctx, span := gtrace.NewSpan(ctx, "Rule.Reset")
	defer span.End()
	res := rulengine.Load(ctx, data, true)
	if !res {
		return rulengine.RuleSize()
	}

	gosutra.LoadInnerRules()
	return rulengine.RuleSize()
}
