package main

import (
	"io"
	"log"
	"sync"
	"testing"
)

func TestHandlerSig(t *testing.T) {
	r, w := io.Pipe()

	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		d := make([]byte, 1)
		for {
			_, err := r.Read(d)
			if err == io.EOF {
				break
			}
			if err != nil {
				panic(err)
			}

			log.Println("read data:", string(d))

			//time.Sleep(time.Millisecond * 100)
		}
	}()

	w.Write([]byte("abc"))
	w.Close()
	wg.Wait()
	r.Close()

}
