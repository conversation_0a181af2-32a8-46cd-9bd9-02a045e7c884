package main

import (
	"encoding/hex"
	"encoding/json"
	"flag"
	"fmt"
	"git.gobies.org/sutra/gosutra"
	"git.gobies.org/sutra/gosutra/cmd/sutratcpdump/protocols"
	"git.gobies.org/sutra/gosutra/internal/genmap"
	wappalyzer "github.com/projectdiscovery/wappalyzergo"
	"log"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"runtime/pprof"
	"strings"
	"sync"
	"time"

	"github.com/google/gopacket"
	"github.com/google/gopacket/examples/util"
	"github.com/google/gopacket/ip4defrag"
	"github.com/google/gopacket/layers" // pulls in all layers decoders
	"github.com/google/gopacket/pcap"
	"github.com/google/gopacket/reassembly"
)

var maxcount = flag.Int("c", -1, "Only grab this many packets, then exit")
var statsevery = flag.Int("stats", 1000, "Output statistics every N packets")
var lazy = flag.Bool("lazy", true, "If true, do lazy decoding")
var nodefrag = flag.Bool("nodefrag", true, "If true, do not do IPv4 defrag")
var checksum = flag.Bool("checksum", false, "Check TCP checksum")

//var nooptcheck = flag.Bool("nooptcheck", false, "Do not check TCP options (useful to ignore MSS on captures with TSO)")
var ignorefsmerr = flag.Bool("ignorefsmerr", false, "Ignore TCP FSM errors")
var allowmissinginit = flag.Bool("allowmissinginit", false, "Support streams without SYN/SYN+ACK/ACK sequence")

// http
var output = flag.String("output", "", "Path to create file for HTTP 200 OK responses")

var hexdump = flag.Bool("dump", false, "Dump HTTP request/response as hex")
var hexdumppkt = flag.Bool("dumppkt", false, "Dump packet as hex")

// capture
var iface = flag.String("i", "eth0", "Interface to read packets from")

//var port = flag.Int("p", 80, "Interface to read packets from")
var fname = flag.String("r", "", "Filename to read from, overrides -i")
var snaplen = flag.Int("s", 65536, "Snap length (number of bytes max to read per packet")
var tstype = flag.String("timestamp_type", "", "Type of timestamps to use")
var promisc = flag.Bool("promisc", true, "Set promiscuous mode")

var memprofile = flag.String("memprofile", "", "Write memory profile")
var pprofbind = flag.String("pprofbind", "", "http pprof bind port")
var websocketBind = flag.String("websocketBind", "", "http pprof bind port")

var debug = flag.Bool("debug", false, "debug or not")
var localEngine = flag.Bool("localEngine", false, "enable local engine or not")
var wappalyzerEngine = flag.Bool("wappalyzerEngine", false, "enable wappalyzer engine or not")
var sutraIP = flag.Bool("sutraIP", false, "enable sutraIP engine or not")

var signalChan chan os.Signal
var sysexit bool = false

var stats struct {
	ipdefrag    int
	missedBytes int
	pkt         int
	sz          int
	totalsz     int
	rejectFsm   int
	//rejectOpt           int
	reject              int
	rejectConnFsm       int
	reassembled         int
	outOfOrderBytes     int
	outOfOrderPackets   int
	biggestChunkBytes   int
	biggestChunkPackets int
	overlapBytes        int
	overlapPackets      int
}

//const closeTimeout time.Duration = time.Hour * 24 // Closing inactive: TODO: from CLI
const closeTimeout time.Duration = time.Minute * 5 // Closing inactive: TODO: from CLI
const timeout time.Duration = time.Minute * 3      // Pending bytes: TODO: from CLI

/*
 * The assembler context
 */
type Context struct {
	CaptureInfo gopacket.CaptureInfo
}

func (c *Context) GetCaptureInfo() gopacket.CaptureInfo {
	return c.CaptureInfo
}

func HandlerSig() {

	for {
		select {
		case <-signalChan:
			fmt.Fprintf(os.Stderr, "\nCaught SIGINT: aborting %v\n", sysexit)
			if sysexit == false {
				sysexit = true
			} else {
				os.Exit(1) //Second ctrl+c system exit
			}
		}
	}
}

func pcapWork(ch chan pair, onClientData func(name string, data interface{}, p *tcpStream),
	onServerData func(name string, data interface{}, p *tcpStream),
	onProtocol func(p *tcpStream)) {
	defer util.Run()()
	var handle *pcap.Handle
	var err error

	defer close(ch)

	if *fname != "" {
		if handle, err = pcap.OpenOffline(*fname); err != nil {
			log.Fatal("PCAP OpenOffline error:", err)
		}
	} else {
		// This is a little complicated because we want to allow all possible options
		// for creating the packet capture handle... instead of all this you can
		// just call pcap.OpenLive if you want a simple handle.
		inactive, err := pcap.NewInactiveHandle(*iface)
		if err != nil {
			log.Fatalf("could not create: %v", err)
		}
		defer inactive.CleanUp()
		if err = inactive.SetSnapLen(*snaplen); err != nil {
			log.Fatalf("could not set snap length: %v", err)
		} else if err = inactive.SetPromisc(*promisc); err != nil {
			log.Fatalf("could not set promisc mode: %v", err)
		} else if err = inactive.SetTimeout(time.Second); err != nil {
			log.Fatalf("could not set timeout: %v", err)
		}
		if *tstype != "" {
			if t, err := pcap.TimestampSourceFromString(*tstype); err != nil {
				log.Fatalf("Supported timestamp types: %v", inactive.SupportedTimestamps())
			} else if err := inactive.SetTimestampSource(t); err != nil {
				log.Fatalf("Supported timestamp types: %v", inactive.SupportedTimestamps())
			}
		}
		if handle, err = inactive.Activate(); err != nil {
			log.Fatal("PCAP Activate error:", err)
		}
		defer handle.Close()
	}
	if len(flag.Args()) > 0 {
		bpffilter := strings.Join(flag.Args(), " ")
		//Info("Using BPF filter %q\n", bpffilter)
		if err = handle.SetBPFFilter(bpffilter); err != nil {
			log.Fatal("BPF filter error:", err)
		}
	} else {

		//bpffilter := fmt.Sprintf("tcp and port %d",*port)
		bpffilter := fmt.Sprintf("tcp")
		if err = handle.SetBPFFilter(bpffilter); err != nil {
			log.Fatal("BPF filter error:", err)
		}

	}

	source := gopacket.NewPacketSource(handle, handle.LinkType())
	source.Lazy = *lazy
	source.NoCopy = true
	//Info("Starting to read packets\n")
	count := 0
	bytes := int64(0)
	start := time.Now()
	defragger := ip4defrag.NewIPv4Defragmenter()

	streamFactory := &tcpStreamFactory{
		OnClientData: func(name string, data interface{}, p *tcpStream) {

		},
		OnServerData: func(name string, data interface{}, p *tcpStream) {

		},
		OnProtocol: func(p *tcpStream) {

		},
	}
	if onServerData != nil {
		streamFactory.OnServerData = onServerData
	}
	if onClientData != nil {
		streamFactory.OnClientData = onClientData
	}
	if onProtocol != nil {
		streamFactory.OnProtocol = onProtocol
	}

	streamPool := reassembly.NewStreamPool(streamFactory)
	assembler := reassembly.NewAssembler(streamPool)
	// 设置配置，否则内存会飙升
	assembler.MaxBufferedPagesPerConnection = 500
	assembler.MaxBufferedPagesTotal = 100000

	signalChan = make(chan os.Signal, 1)
	signal.Notify(signalChan, os.Interrupt)
	go HandlerSig()

	for packet := range source.Packets() {
		count++
		//Debug("PACKET #%d\n", count)
		data := packet.Data()
		bytes += int64(len(data))
		if *hexdumppkt {
			log.Printf("Packet content (%d/0x%x)\n%s\n", len(data), len(data), hex.Dump(data))
		}

		// defrag the IPv4 packet if required
		if !*nodefrag {
			ip4Layer := packet.Layer(layers.LayerTypeIPv4)
			if ip4Layer == nil {
				continue
			}
			ip4 := ip4Layer.(*layers.IPv4)
			l := ip4.Length
			newip4, err := defragger.DefragIPv4(ip4)
			if err != nil {
				// Error while de-fragmenting defrag: fragment too small (handcrafted? 7 < 8)
				log.Println("Error while de-fragmenting", err)
				continue
			} else if newip4 == nil {
				//Debug("Fragment...\n")
				continue // packet fragment, we don't have whole packet yet.
			}
			if newip4.Length != l {
				stats.ipdefrag++
				//Debug("Decoding re-assembled packet: %s\n", newip4.NextLayerType())
				pb, ok := packet.(gopacket.PacketBuilder)
				if !ok {
					panic("Not a PacketBuilder")
				}
				nextDecoder := newip4.NextLayerType()
				nextDecoder.Decode(newip4.Payload, pb)
			}
		}

		tcp := packet.Layer(layers.LayerTypeTCP)
		if tcp != nil {
			tcp := tcp.(*layers.TCP)
			if *checksum {
				err := tcp.SetNetworkLayerForChecksum(packet.NetworkLayer())
				if err != nil {
					log.Fatalf("Failed to set network layer for checksum: %s\n", err)
				}
			}
			c := Context{
				CaptureInfo: packet.Metadata().CaptureInfo,
			}
			stats.totalsz += len(tcp.Payload)

			// 这里容易造成一个死锁：tcpstream.run退出，但是没有调用done去close消息队列，导致往里面写数据（超过50个）会卡死
			// 而FlushWithOptions没有机会被执行触发complete的通知，所以要在run的defer里面调用done
			assembler.AssembleWithContext(packet.NetworkLayer().NetworkFlow(), tcp, &c)
		}
		if count%*statsevery == 0 {
			ref := packet.Metadata().CaptureInfo.Timestamp
			flushed, closed := assembler.FlushWithOptions(reassembly.FlushOptions{T: ref.Add(-timeout), TC: ref.Add(-closeTimeout)})
			log.Printf("Forced flush: %d flushed, %d closed (%s) \n", flushed, closed, ref)
			//Debug("Forced flush: %d flushed, %d closed (%s)", flushed, closed, ref)
		}

		done := *maxcount > 0 && count >= *maxcount
		if count%*statsevery == 0 || done || sysexit {
			fmt.Fprintf(os.Stderr, "Processed %v packets (%v bytes) in %v \n", count, bytes, time.Since(start))
		}
		if sysexit {
			break
		}
	}

	closed := assembler.FlushAll()
	log.Printf("Final flush: %d closed\n", closed)
	//if outputLevel >= 2 {
	//	streamPool.Dump()
	//}
	streamPool.Dump()

	if *memprofile != "" {
		f, err := os.Create(*memprofile)
		if err != nil {
			log.Fatal(err)
		}
		pprof.WriteHeapProfile(f)
		f.Close()
	}

	streamFactory.WaitGoRoutines()
	log.Println(assembler.Dump())
	//Debug("%s\n", assembler.Dump())
	if !*nodefrag {
		fmt.Printf("IPdefrag:\t\t%d\n", stats.ipdefrag)
	}
	fmt.Printf("TCP stats:\n")
	fmt.Printf(" missed bytes:\t\t%d\n", stats.missedBytes)
	fmt.Printf(" total packets:\t\t%d\n", stats.pkt)
	fmt.Printf(" rejected FSM:\t\t%d\n", stats.rejectFsm)
	//fmt.Printf(" rejected Options:\t%d\n", stats.rejectOpt)
	fmt.Printf(" rejected:\t%d\n", stats.reject)
	fmt.Printf(" reassembled bytes:\t%d\n", stats.sz)
	fmt.Printf(" total TCP bytes:\t%d\n", stats.totalsz)
	fmt.Printf(" conn rejected FSM:\t%d\n", stats.rejectConnFsm)
	fmt.Printf(" reassembled chunks:\t%d\n", stats.reassembled)
	fmt.Printf(" out-of-order packets:\t%d\n", stats.outOfOrderPackets)
	fmt.Printf(" out-of-order bytes:\t%d\n", stats.outOfOrderBytes)
	fmt.Printf(" biggest-chunk packets:\t%d\n", stats.biggestChunkPackets)
	fmt.Printf(" biggest-chunk bytes:\t%d\n", stats.biggestChunkBytes)
	fmt.Printf(" overlap packets:\t%d\n", stats.overlapPackets)
	fmt.Printf(" overlap bytes:\t\t%d\n", stats.overlapBytes)
	//fmt.Printf("Errors: %d\n", errors)
	//for e, _ := range errorsMap {
	//	fmt.Printf(" %s:\t\t%d\n", e, errorsMap[e])
	//}
}

// printData 输出数据
func printData(sutraCli *gosutra.Client, pInfo map[string]interface{}, s time.Time) {
	if *sutraIP {
		products, err := sutraCli.IPProducts(pInfo["ip"].(string))
		if err == nil && len(products) > 0 {
			pInfo["products"] = append(pInfo["products"].([]string), products...)
		}
	}

	pInfo["cost"] = time.Since(s).String()

	d, err := json.Marshal(pInfo)
	if err != nil {
		log.Println("[WARNING] json failed:", err)
		return
	}
	fmt.Println(string(d))
	broadCast(d)
}

func processProtocol(wg *sync.WaitGroup, p *tcpStream, sutraCli *gosutra.Client) {
	defer wg.Done()

	s := time.Now()
	pInfo := map[string]interface{}{
		"timestamp":   s.String(),
		"ip":          p.dstip,
		"port":        p.dstport,
		"client_ip":   p.srcip,
		"client_port": p.srcport,
		"products":    []string{p.proto.Info()["protocol"].(string)},
	}
	for k, v := range p.proto.Info() {
		if _, ok := pInfo[k]; !ok {
			pInfo[k] = v
		}
	}

	printData(sutraCli, pInfo, s)
}

func processRtspResponse(wg *sync.WaitGroup, resp *protocols.RtspParsedResponse, p *tcpStream, sutraCli *gosutra.Client) {
	defer wg.Done()
	s := time.Now()

	pInfo := map[string]interface{}{
		"timestamp":   s.String(),
		"ip":          p.dstip,
		"port":        p.dstport,
		"client_ip":   p.srcip,
		"client_port": p.srcport,
		"protocol":    "rtsp",
		"header":      resp.Header,
		"server":      resp.Server,
		"host":        resp.Host,
		"url":         resp.URL,
		"products":    []string{p.proto.Info()["protocol"].(string)},
	}
	for k, v := range p.proto.Info() {
		if _, ok := pInfo[k]; !ok {
			pInfo[k] = v
		}
	}

	printData(sutraCli, pInfo, s)
}

func processHttpResponse(wg *sync.WaitGroup, sutraCli *gosutra.Client, wappalyzerClient *wappalyzer.Wappalyze, p pair) {
	defer wg.Done()

	s := time.Now()
	httpInfo := map[string]interface{}{
		"timestamp":   s.String(),
		"ip":          p.p.dstip,
		"port":        p.p.dstport,
		"client_ip":   p.p.srcip,
		"client_port": p.p.srcport,
		"protocol":    "http",
		"header":      p.resp.Header,
		//"raw_header":  p.resp.RawHeader,
		"body":   string(p.resp.Body),
		"server": p.resp.Server,
		"host":   p.resp.Host,
		"url":    p.resp.URL,
	}

	if p.resp.RawHeader.Get("Upgrade") == "websocket" {
		httpInfo["products"] = []string{"websocket"}
	}

	if !strings.Contains(p.resp.ContentType, "html") {
		delete(httpInfo, "body")
		httpInfo["cost"] = time.Since(s).String()
		v, err := json.Marshal(httpInfo)
		if err != nil {
			log.Println("[WARNING] json failed:", err)
			return
		}
		fmt.Println(string(v))
		broadCast(v)
		return
	}

	if v := genmap.GenProductsInfoFromMap(httpInfo, sutraCli, wappalyzerClient, s); v != nil {
		fmt.Println(string(v))
		broadCast(v)
	}
}

type pair struct {
	resp *protocols.HttpParsedResponse
	p    *tcpStream
}

func main() {

	log.Println("sutratcpdump initing...")

	flag.Parse()
	if *localEngine {
		gosutra.LoadInnerRules()
	}

	//
	if len(*pprofbind) > 0 {
		go func() {
			http.ListenAndServe(*pprofbind, nil)
		}()
	}

	// websocket
	go startWebsocket()

	sutraCli := gosutra.NewClient(gosutra.WithCloudQuery(true),
		gosutra.WithQueryRawJson(false),
		gosutra.WithServer("http://api.sutrad.org/"),
		gosutra.WithHashCacheMinute(10))

	var wappalyzerClient *wappalyzer.Wappalyze
	var err error
	if *wappalyzerEngine {
		wappalyzerClient, err = wappalyzer.New()
		if err != nil {
			panic(err)
		}
	}

	var wg sync.WaitGroup

	ch := make(chan pair, 100)
	go pcapWork(
		ch,
		func(name string, data interface{}, p *tcpStream) {
			switch name {
			case "http":
				if *debug {
					r := data.(*http.Request)
					log.Println("new http request:", r.URL.String())
				}
			}
		},
		func(name string, data interface{}, p *tcpStream) {
			// on server data
			switch name {
			case "http":
				r := data.(*protocols.HttpParsedResponse)
				if *debug {
					log.Println("new http response:", r.URL)
				}

				ch <- pair{
					resp: r,
					p:    p,
				}
			case "rtsp":
				r := data.(*protocols.RtspParsedResponse)
				wg.Add(1)
				go processRtspResponse(&wg, r, p, sutraCli)
			}
		},
		func(p *tcpStream) {
			d := p.proto.Info()
			switch d["name"] {
			case "http":
			default:
				wg.Add(1)
				go processProtocol(&wg, p, sutraCli)
			}
		})

	for i := range ch {
		wg.Add(1)
		go processHttpResponse(&wg, sutraCli, wappalyzerClient, i)
	}
	wg.Wait()
}
