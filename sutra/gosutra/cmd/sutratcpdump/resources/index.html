
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>sutratcpdump</title>
    <link rel="stylesheet" href="/static/index.css">
    <link rel="stylesheet" href="/static/base.css">
</head>
<body>
<div id="app">
    <div class="operate">
        <el-button type="primary" v-if="!closeWs" @click="closeSocket">暂停</el-button>
        <el-button type="primary" v-else @click="openSocket">开启</el-button>
    </div>

    <div class="config-sec">
        <el-checkbox-group v-model="protocolCheckList">
            <el-checkbox v-for="(item, index) in protocolList" :label="item" :key="index"></el-checkbox>
        </el-checkbox-group>
    </div>

    <el-table
            :data="tableData"
            style="width: 100%; table-layout: fixed;">
        <el-table-column
                prop="ip"
                label="Destination"
                width="180">
            <template slot-scope="scope" class="gb-com-td">
                {{ scope.row.ip }}:{{ scope.row.port }}
            </template>
        </el-table-column>
        <el-table-column
                prop="client_ip"
                label="Source"
                width="180">
            <template slot-scope="scope" class="gb-com-td">
                {{ scope.row.client_ip }}:{{ scope.row.client_port }}
            </template>
        </el-table-column>
        <el-table-column
                prop="cost"
                label="Cost">
            <template slot-scope="scope">
                <div :class="calcCostStyle(scope.row.cost)">{{ scope.row.cost }}</div>
            </template>
        </el-table-column>
        <el-table-column
                prop="ehash"
                label="EHASH">
            <template slot-scope="scope">
                <div>
                    <a @click="toFofa(scope.row)">{{scope.row.ehash}}</a>
                </div>
            </template>
        </el-table-column>
        <el-table-column
                :filters="protocolFilters"
                :filter-method="protocolFilterHandler"
                prop="protocol"
                label="Protocol">
        </el-table-column>
        <el-table-column
                prop="host"
                label="URL">
            <template slot-scope="scope" class="gb-com-td">
                {{ scope.row.host }}{{ scope.row.url }}
            </template>
        </el-table-column>
        <el-table-column
                prop="products"
                label="Products">
            <template slot-scope="scope" class="gb-com-td">
                <div class="gb-com-td">
                    <div class="clearfix">
                          <span v-for="(item, index) in scope.row.products" :key="index">
                            {{ item }}
                          </span>
                        <p v-if="scope.row.products && scope.row.products.length == 0">-</p>
                    </div>
                </div>
            </template>
        </el-table-column>
        <el-table-column
                v-if="false"
                prop="products"
                label="Products">
            <template slot-scope="scope" class="gb-com-td">
                <div class="gb-com-td">
                    <el-tooltip effect="dark" placement="top" content="Application Layer" :open-delay="500" :visible-arrow="false">
                        <div class="clearfix" :class="{empty: !scope.row.fiveLvData || scope.row.fiveLvData.length == 0}">
                <span v-for="(item, index) in scope.row.fiveLvData" :key="index">
                  {{ item.product }}
                </span>
                            <p v-if="!scope.row.fiveLvData || scope.row.fiveLvData.length == 0">-</p>
                        </div>
                    </el-tooltip>
                    <el-tooltip effect="dark" placement="top" content="Support Layer" :open-delay="500" :visible-arrow="false">
                        <div class="clearfix" :class="{empty: !scope.row.firstLvData || scope.row.firstLvData.length == 0}">
                <span v-for="(item, index) in scope.row.firstLvData" :key="index">
                  {{ item.product }}
                </span>
                            <p v-if="!scope.row.firstLvData || scope.row.firstLvData.length == 0">-</p>
                        </div>
                    </el-tooltip>
                    <el-tooltip effect="dark" placement="top" content="Service Layer" :open-delay="500" :visible-arrow="false">
                        <div class="clearfix" :class="{empty: !scope.row.secLvData || scope.row.secLvData.length == 0}">
                <span v-for="(item, index) in scope.row.secLvData" :key="index">
                  {{ item.product }}
                </span>
                            <p v-if="!scope.row.secLvData || scope.row.secLvData.length == 0">-</p>
                        </div>
                    </el-tooltip>
                    <el-tooltip effect="dark" placement="top" content="System Layer" :open-delay="500" :visible-arrow="false">
                        <div class="clearfix" :class="{empty: !scope.row.thrLvData || scope.row.thrLvData.length == 0}">
                <span v-for="(item, index) in scope.row.thrLvData" :key="index">
                  {{ item.product }}
                </span>
                            <p v-if="!scope.row.thrLvData || scope.row.thrLvData.length == 0">-</p>
                        </div>
                    </el-tooltip>
                    <el-tooltip effect="dark" placement="top" content="Hardware Layer" :open-delay="500" :visible-arrow="false">
                        <div class="clearfix" :class="{empty: !scope.row.fourLvData || scope.row.fourLvData.length == 0}">
                <span v-for="(item, index) in scope.row.fourLvData" :key="index">
                  {{ item.product }}
                </span>
                            <p v-if="!scope.row.fourLvData || scope.row.fourLvData.length == 0">-</p>
                        </div>
                    </el-tooltip>
                </div>
            </template>
        </el-table-column>
        <el-table-column
                prop="timestamp"
                label="Timestamp">
        </el-table-column>
    </el-table>
</div>
<script src="/static/jquery.js"></script>
<script src="/static/base64.js"></script>
<script src="/static/vue.js"></script>
<script src="/static/index.js"></script>
<script>
    var vm = new Vue({
        el: '#app',
        data: {
            tableData: [
                {
                    ehash: '11',
                    products: [],
                    // 应用层
                    fiveLvData: null,
                    // 支撑层
                    firstLvData: null,
                    // 服务层
                    secLvData: [{
                        product: '应用1'
                    },
                        {
                            product: '应用2'
                        }],
                    // 系统层
                    thrLvData: [],
                    // 硬件层
                    fourLvData: [],
                    cost: '2s',
                    protocol: 'http'
                },
                {
                    ehash: '22',
                    products: [],
                    // 应用层
                    fiveLvData: [
                        {
                            product: '应用1'
                        },
                        {
                            product: '应用2'
                        }
                    ],
                    // 支撑层
                    firstLvData: [
                    ],
                    // 服务层
                    secLvData: [],
                    // 系统层
                    thrLvData: [],
                    // 硬件层
                    fourLvData: [],
                    cost: '2µs',
                    protocol: 'ftp'
                },
                {
                    ehash: '22',
                    products: [],
                    // 应用层
                    fiveLvData: [
                        {
                            product: '应用1'
                        },
                        {
                            product: '应用2'
                        }
                    ],
                    // 支撑层
                    firstLvData: [
                    ],
                    // 服务层
                    secLvData: [],
                    // 系统层
                    thrLvData: [],
                    // 硬件层
                    fourLvData: [],
                    cost: '2ms',
                    protocol: 'ftp'
                }
            ],
            ws: '',
            closeWs: false,
            protocolList: ['http', 'https', 'ssh', 'mysql', 'redis', 'rtsp'],
            protocolCheckList: ['http', 'https', 'ssh', 'mysql', 'redis', 'rtsp']
        },
        computed: {
            protocolFilters() {
                let protocolFilters = [];
                let protocolArr = this.tableData.map((v, k) => { return v.protocol });
                protocolArr = [...new Set(protocolArr)];
                protocolArr.forEach((v, k) => {
                    protocolFilters.push({
                        text: v,
                        value: v
                    })
                })
                return protocolFilters;
            }
        },
        methods: {
            initWs () {
                let that = this;

                this.ws = new WebSocket("{{{WS}}}");

                this.ws.onopen = function () {
                    console.log('connect ok');
                }

                this.ws.onmessage = function (evt) {
                    console.log('on data');
                    var result= evt.data;
                    var data = JSON.parse(event.data)
                    console.log(data);
                    tdata = vm.tableData;
                    tdata.splice(0, 0, data); // 总数据
                    // 根据当前选择得协议，进行数据过滤
                    tdata = tdata.filter((v, k) => { return that.protocolCheckList.includes(v.protocol) });
                    vm.tableData = tdata.slice(0, 20); // 赋值
                }

                this.ws.onclose = function () {
                    if (!that.closeWs) vm.initWs();
                }
            },
            getBase64 (value) {
                return $.base64.encode(value);
            },
            toFofa (row) {
                let handleValue = `fidv2="${row.fohash}"`;
                let str = this.getBase64(handleValue);
                window.open('https://fofapro.com/result?qbase64=' + str, '_blank');
            },
            // 关闭socket
            closeSocket () {
                this.closeWs = true;
                this.ws.close();
                this.$message({
                    message: "关闭成功",
                    type: "success"
                });
            },
            // 开启socket
            openSocket () {
                this.closeWs = false;
                this.initWs();
            },
            // 协议过滤
            protocolFilterHandler(value, row, column) {
                const property = column['property'];
                return row[property] === value;
            },
            // 计算cost样式
            calcCostStyle (cost) {
                if (cost.includes('µs')) {
                    return 'cost-green';
                } else if (cost.includes('ms')) {
                    return '';
                } else {
                    return 'cost-red';
                }
            }
        }
    })
    vm.initWs();
</script>
</body>
</html>