package main

import (
	"context"
	"fmt"
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	"github.com/google/gopacket/reassembly"
	"sync"
	"time"
)

/*
 * The TCP factory: returns a new Stream
 */
type tcpStreamFactory struct {
	wg           sync.WaitGroup
	OnClientData func(name string, data interface{}, t *tcpStream)
	OnServerData func(name string, data interface{}, t *tcpStream)
	OnProtocol   func(t *tcpStream)
}

func (factory *tcpStreamFactory) New(net, transport gopacket.Flow, tcp *layers.TCP, ac reassembly.AssemblerContext) reassembly.Stream {
	// net中存的是srcip -> dstip； transport中存的是srcport -> dstport
	sip, dip := net.Endpoints()
	srcip := sip.String()
	dstip := dip.String()

	fsmOptions := reassembly.TCPSimpleFSMOptions{
		SupportMissingEstablishment: *allowmissinginit,
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*5)
	stream := &tcpStream{
		ctx:       ctx,
		cancel:    cancel,
		net:       net,
		transport: transport,
		tcpstate:  reassembly.NewTCPSimpleFSM(fsmOptions),
		//clientIdent: net.String() + ":" + transport.String(),
		//serverIdent: net.Reverse().String() + ":" + transport.Reverse().String(),
		//optchecker:  reassembly.NewTCPOptionCheck(),
		factory: factory,
		srcport: fmt.Sprintf("%d", tcp.SrcPort),
		dstport: fmt.Sprintf("%d", tcp.DstPort),
		srcip:   srcip,
		dstip:   dstip,

		//reqBufCh:  make(chan []byte, 100),
		//respBufCh: make(chan []byte, 100),
	}

	return stream
}

func (factory *tcpStreamFactory) WaitGoRoutines() {
	factory.wg.Wait()
}
