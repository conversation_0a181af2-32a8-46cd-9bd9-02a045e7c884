/*
协议解析器，分为两种类型：
- 简单的提取协议，如mysql
- 深度解析协议，如http要进行ehash的提取

参考：
- https://github.com/ntop/nDPI/tree/dev/src/lib/protocols
- https://github.com/zeek/zeek/tree/master/src/analyzer/protocol

目前看来nDPI是浅的，zeek是深度的
*/
package protocols

import (
	"bufio"
	"context"
	"errors"
	"sync"
)

var (
	ErrNotImpl = errors.New("protocol not impl")
)

type IsValidProtocol func(data []byte) bool // 是否合法
type NewProtocolFunc func(id string) Protocol

type Protocol interface {
	Info() map[string]interface{}
	Accept(s2c bool, data []byte) (bool, bool) // accept, close
	DoClient(ctx context.Context, reader *bufio.Reader) (interface{}, error)
	DoServer(ctx context.Context, reader *bufio.Reader) (interface{}, error)
}

type ProtocolMather struct {
	protocols []NewProtocolFunc
	sync.Mutex
}

func (pm *ProtocolMather) Resister(p NewProtocolFunc) {
	pm.Lock()
	pm.Unlock()
	pm.protocols = append(pm.protocols, p)
}

func (pm *ProtocolMather) Accept(s2c bool, id string, data []byte) (Protocol, bool) {
	for i := range pm.protocols {
		p := pm.protocols[i](id)
		if ok, closed := p.Accept(s2c, data); ok {
			return p, closed
		}
	}
	return nil, true
}

func Accept(s2c bool, id string, data []byte) (Protocol, bool) {
	return globalProtocolMather.Accept(s2c, id, data)
}

var (
	globalProtocolMather = &ProtocolMather{}
)
