package protocols

import (
	"encoding/binary"
)

/*
Distributed Computing Environment / Remote Procedure Call (DCE/RPC) Bind, Fragment: Single, FragLen: 160, Call: 2
    Version: 5
    Version (minor): 0
    Packet type: Bind (11)
    Packet Flags: 0x03
    Data Representation: 10000000 (Order: Little-endian, Char: ASCII, Float: IEEE)
    Frag Length: 160
    Auth Length: 0
    Call ID: 2
    Max Xmit Frag: 5840
    Max Recv Frag: 5840
    Assoc Group: 0x00000000
    Num Ctx Items: 3
    Ctx Item[1]: Context ID:0, EPMv4, 32bit NDR
        Context ID: 0
        Num Trans Items: 1
        Abstract Syntax: EPMv4 V3.0
        Transfer Syntax[1]: 32bit NDR V2
            Transfer Syntax: 32bit NDR UUID:8a885d04-1ceb-11c9-9fe8-08002b104860
            ver: 2
    Ctx Item[2]: Context ID:1, EPMv4, 64bit NDR
        Context ID: 1
        Num Trans Items: 1
        Abstract Syntax: EPMv4 V3.0
        Transfer Syntax[1]: 64bit NDR V1
            Transfer Syntax: 64bit NDR UUID:71710533-beba-4937-8319-b5dbef9ccc36
            ver: 1
    Ctx Item[3]: Context ID:2, EPMv4, Bind Time Feature Negotiation
        Context ID: 2
        Num Trans Items: 1
        Abstract Syntax: EPMv4 V3.0
        Transfer Syntax[1]: Bind Time Feature Negotiation V1
            Transfer Syntax: Bind Time Feature Negotiation UUID:6cb71c2c-**************-000000000000
            Bind Time Features: 0x0003, Security Context Multiplexing Supported, Keep Connection On Orphan Supported
            ver: 1

0000   05 00 0b 03 10 00 00 00 a0 00 00 00 02 00 00 00   ................
0010   d0 16 d0 16 00 00 00 00 03 00 00 00 00 00 01 00   ................
0020   08 83 af e1 1f 5d c9 11 91 a4 08 00 2b 14 a0 fa   .....]......+...
0030   03 00 00 00 04 5d 88 8a eb 1c c9 11 9f e8 08 00   .....]..........
0040   2b 10 48 60 02 00 00 00 01 00 01 00 08 83 af e1   +.H`............
0050   1f 5d c9 11 91 a4 08 00 2b 14 a0 fa 03 00 00 00   .]......+.......
0060   33 05 71 71 ba be 37 49 83 19 b5 db ef 9c cc 36   3.qq..7I.......6
0070   01 00 00 00 02 00 01 00 08 83 af e1 1f 5d c9 11   .............]..
0080   91 a4 08 00 2b 14 a0 fa 03 00 00 00 2c 1c b7 6c   ....+.......,..l
0090   12 98 40 45 03 00 00 00 00 00 00 00 01 00 00 00   ..@E............

*/

type dcerpcProtocol struct {
	baseFirstPacketProtocol
}

func isDceRpcPacket(data []byte, p *baseFirstPacketProtocol) bool {
	l := uint16(len(data))
	if l < 10 {
		return false
	}

	payloadLength := binary.LittleEndian.Uint16(data[8:10])
	if payloadLength != l {
		return false
	}

	switch data[0x2] {
	case 0x0b:
		//bind
		return true
	}
	return false
}

func NewDceRpcProtocol(id string) Protocol {
	baseProtocol := NewBaseClientFirstProtocol(id, "dcerpc", isDceRpcPacket, "func")

	return &dcerpcProtocol{
		baseProtocol,
	}
}

func init() {
	globalProtocolMather.Resister(NewDceRpcProtocol)
}
