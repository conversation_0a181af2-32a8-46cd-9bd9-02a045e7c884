package protocols

import (
	"encoding/binary"
)

type mysqlProtocol struct {
	baseFirstPacketProtocol
}

func isMysqlPacket(data []byte, p *baseFirstPacketProtocol) bool {
	l := uint32(len(data))
	if l < 5 {
		return false
	}
	offset := 0
	payloadLength := binary.LittleEndian.Uint32(data[:4])
	if payloadLength&0x00ffffff != l-4 {
		return false
	}
	offset += 4
	//sequenceId := payloadLength & 0xff000000

	switch data[offset] {
	case 0xff:
		//ERR response
	case 0x09, 0x0a:
		p.userData["protocolVersion"] = data[offset]
		offset += 1
		var version string
		for ; offset < int(l) && data[offset] != 0x00; offset++ {
			version += string(data[offset])
		}
		p.userData["version"] = version
	}
	return true
}

// https://dev.mysql.com/doc/internals/en/mysql-packet.html
// https://github.com/elastic/beats/blob/main/packetbeat/protos/mysql/mysql.go
func NewMysqlProtocol(id string) Protocol {
	baseProtocol := NewBaseServerFirstProtocol(id, "mysql", isMysqlPacket, "func")

	return &mysqlProtocol{
		baseProtocol,
	}
}

func init() {
	globalProtocolMather.Resister(NewMysqlProtocol)
}
