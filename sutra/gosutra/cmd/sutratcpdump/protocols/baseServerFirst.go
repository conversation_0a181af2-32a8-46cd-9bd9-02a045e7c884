package protocols

import (
	"bufio"
	"bytes"
	"context"
	"regexp"
)

/*
baseFirstPacketProtocol 满足如下条件：
- 有一段先先发起包
- 第一个包可以正则匹配
- 只能判断一个包
*/
type baseFirstPacketProtocol struct {
	s2c          bool // 方向
	id           string
	protocol     string
	matchData    interface{}
	matchOperate string

	userData map[string]interface{} // 用户自定义结构，上层设置或者获取
}

func NewBaseServerFirstProtocol(id string, protocol string, matchData interface{}, matchOperate string) baseFirstPacketProtocol {
	return baseFirstPacketProtocol{
		s2c:          true,
		id:           id,
		protocol:     protocol,
		matchData:    matchData,
		matchOperate: matchOperate,
		userData:     make(map[string]interface{}),
	}
}

func NewBaseClientFirstProtocol(id string, protocol string, matchData interface{}, matchOperate string) baseFirstPacketProtocol {
	return baseFirstPacketProtocol{
		s2c:          false,
		id:           id,
		protocol:     protocol,
		matchData:    matchData,
		matchOperate: matchOperate,
		userData:     make(map[string]interface{}),
	}
}

func (h *baseFirstPacketProtocol) Info() map[string]interface{} {
	m := map[string]interface{}{
		"protocol": h.protocol,
		"id":       h.id,
	}

	for k, v := range h.userData {
		m[k] = v
	}
	return m
}

func (h *baseFirstPacketProtocol) Accept(s2c bool, data []byte) (valid bool, close bool) {
	if s2c != h.s2c {
		return false, true
	}

	if len(data) < 5 {
		return false, true
	}

	switch h.matchOperate {
	case "regex":
		ok, err := regexp.Match(h.matchData.(string), data)
		if err != nil {
			panic(err)
		}
		if !ok {
			return false, true
		}
	case "prefix":
		if !bytes.HasPrefix(data, []byte(h.matchData.(string))) {
			return false, true
		}
	case "func":
		if !h.matchData.(func(data []byte, protocol *baseFirstPacketProtocol) bool)(data, h) {
			return false, true
		}
	default:
		panic("unknown match operate")
	}

	return true, true
}

func (h *baseFirstPacketProtocol) DoClient(ctx context.Context, reader *bufio.Reader) (interface{}, error) {
	return nil, ErrNotImpl
}

func (h *baseFirstPacketProtocol) DoServer(ctx context.Context, reader *bufio.Reader) (interface{}, error) {
	return nil, ErrNotImpl
}
