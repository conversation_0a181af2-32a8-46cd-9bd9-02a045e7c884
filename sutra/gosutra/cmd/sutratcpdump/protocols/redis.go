package protocols

import (
	"bytes"
	"errors"
	"strconv"
)

type redisProtocol struct {
	baseFirstPacketProtocol
}

var ErrNoMoreBytes = errors.New("No more bytes")

func UntilCRLF(data []byte) ([]byte, error) {
	for i, byte := range data {
		if byte != '\r' {
			continue
		}

		if len(data) < i+2 {
			return nil, ErrNoMoreBytes
		}

		if data[i+1] != '\n' {
			// false alarm, continue
			continue
		}

		// yay, found
		return data[:i], nil
	}

	return nil, ErrNoMoreBytes
}

// value, ok
func parseSimpleString(data []byte) (string, bool) {
	line, err := UntilCRLF(data)
	if err != nil {
		return "", false
	}

	return string(line[1:]), true
}

func parseInt(data []byte) (string, bool) {
	value, ok := parseSimpleString(data)
	if ok {
		if _, err := strconv.Atoi(value); err != nil {
			return "", false
		}
	}
	return value, ok
}

// https://github.com/elastic/beats/blob/main/packetbeat/protos/redis/redis.go
func isRedisPacket(data []byte, p *baseFirstPacketProtocol) bool {
	if len(data) < 4 {
		return false
	}
	if !bytes.Contains(data, []byte("\r\n")) {
		return false
	}

	switch data[0] {
	case '*', '$', ':':
		_, ok := parseInt(data)
		if !ok {
			return false
		}
	case '+', '-':
		_, ok := parseSimpleString(data)
		if !ok {
			return false
		}
	default:
		return false
	}

	return true
}

func NewRedisProtocol(id string) Protocol {
	baseProtocol := NewBaseClientFirstProtocol(id, "redis", isRedisPacket, "func")

	return &redisProtocol{
		baseProtocol,
	}
}

func init() {
	globalProtocolMather.Resister(NewRedisProtocol)
}
