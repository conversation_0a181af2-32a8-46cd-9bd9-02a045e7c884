/*
根据原始的json进行字段聚合

只能聚合字段为string的类型

	echo -e '{"a":1,"b":1,"c":1}\n{"a":1,"b":2,"c":3}\n' | ./aggsjson -aggsKey a | jq
	{
	  "a": 1,
	  "records": [
		{
		  "a": 1,
		  "b": 1,
		  "c": 1
		},
		{
		  "a": 1,
		  "b": 2,
		  "c": 3
		}
	  ],
	  "size": 2
	}

	echo -e '{"a":1,"b":1,"c":1}\n{"a":1,"b":2,"c":3}\n' | ./aggsjson -aggsKey a -removeKeys b| jq
	{
	  "a": 1,
	  "records": [
		{
		  "a": 1,
		  "c": 1
		},
		{
		  "a": 1,
		  "c": 3
		}
	  ],
	  "size": 2
	}

	echo -e '{"a":1,"b":1,"c":1}\n{"a":1,"b":2,"c":3}\n' | ./aggsjson -aggsKey a -keepKeys c| jq
	{
	  "a": 1,
	  "records": [
		{
		  "c": 1
		},
		{
		  "c": 3
		}
	  ],
	  "size": 2
	}

	echo -e '{"a":1,"b":1,"c":1}\n{"a":1,"b":2,"c":3}\n' | ./aggsjson -aggsKey a -keepKeys c -appendKeyValues 'fofa_query="type=subdomain"' | jq
	{
	  "a": 1,
	  "fofa_query": "type=subdomain",
	  "records": [
		{
		  "c": 1
		},
		{
		  "c": 3
		}
	  ],
	  "size": 2
	}

	echo -e '{"a":"test","b":1,"c":1}\n{"a":"test","b":2,"c":3}\n' | ./aggsjson -aggsKey a -keepKeys c -appendKeyValues 'fofa_query="type={{{a}}}"' | jq
	{
	  "a": "test",
	  "fofa_query": "type=test",
	  "records": [
		{
		  "c": 1
		},
		{
		  "c": 3
		}
	  ],
	  "size": 2
	}

*/
package main

import (
	"bufio"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"
)

var (
	aggsKey         string
	keepKeys        []string
	removeKeys      []string
	appendKeyValues [][]string
	fieldUniq       bool // 是否字段聚合
	globalMap       sync.Map
)

func processMap(m map[string]interface{}) map[string]interface{} {
	if len(keepKeys) == 0 && len(removeKeys) == 0 {
		return m
	}
	if len(keepKeys) > 0 {
		newM := make(map[string]interface{})
		for _, k := range keepKeys {
			if len(k) == 0 {
				panic("key is empty")
			}
			newM[k] = m[k]
		}
		return newM
	}
	if len(removeKeys) > 0 {
		for _, k := range removeKeys {
			delete(m, k)
		}
		return m
	}

	return nil
}

func check(line string) {
	var m map[string]interface{}
	if err := json.Unmarshal([]byte(line), &m); err != nil {
		log.Println("[ERROR]", err)
		return
	}

	if value, ok := m[aggsKey]; !ok {
		panic("aggsKey not exsits")
	} else {
		m = processMap(m)
		if v, exists := globalMap.Load(value); exists {
			if fieldUniq {

			} else {
				*v.(*[]map[string]interface{}) = append(*v.(*[]map[string]interface{}), m)
			}
			log.Println(aggsKey, value, v)
		} else {
			globalMap.Store(value, &[]map[string]interface{}{
				m,
			})
			log.Println(m)
		}
	}

}

func processInput(reader io.Reader) bool {
	scanner := bufio.NewScanner(reader)
	scanner.Buffer([]byte{}, bufio.MaxScanTokenSize*1024)
	for scanner.Scan() { // internally, it advances token based on sperator
		line := scanner.Text()
		if len(line) > 0 {
			check(line)
		}
	}

	if err := scanner.Err(); err != nil {
		log.Println(err)
		return false
	}

	return true
}

func processFile(fileName string) error {
	f, err := os.Open(fileName)
	if err != nil {
		return err
	}
	defer f.Close()
	processInput(f)
	return nil
}

// replaceVariable 替换变量 {{{}}}的格式
func replaceVariable(m map[string]interface{}, value string) string {
	if matches := regexp.MustCompile(`{{{(.*?)}}}`).FindAllStringSubmatch(value, -1); len(matches) > 0 {
		for _, v := range matches {
			variableName := v[1]
			varValue := fmt.Sprintf("%v", m[variableName])
			if len(varValue) > 0 {
				value = strings.Replace(value, v[0], varValue, -1)
			}
		}
	}
	return value
}

func main() {
	var removeKeysStr, keepKeysStr, appendKeyValueStr string
	flag.StringVar(&aggsKey, "aggsKey", "", "aggs key, every json should has this key")
	flag.StringVar(&removeKeysStr, "removeKeys", "", "split with ','")
	flag.StringVar(&keepKeysStr, "keepKeys", "", "split with ','")
	flag.StringVar(&appendKeyValueStr, "appendKeyValues", "", "split with ',', eahc pair is key=value")
	flag.BoolVar(&fieldUniq, "fieldUniq", false, "split with ','")
	flag.Parse()

	if len(aggsKey) == 0 {
		log.Println("[ERROR] no aggsKey")
		return
	}

	if len(keepKeys) != 0 && len(removeKeys) != 0 {
		log.Println("[ERROR] both keepKeys and removeKeys exists")
		return
	}

	if len(removeKeysStr) > 0 {
		removeKeys = strings.Split(removeKeysStr, ",")
	}
	if len(keepKeysStr) > 0 {
		keepKeys = strings.Split(keepKeysStr, ",")
	}

	if len(appendKeyValueStr) > 0 {
		for _, kv := range strings.Split(appendKeyValueStr, ",") {
			if len(kv) == 0 {
				continue
			}
			if kv[0] == '"' {
				if v, err := strconv.Unquote(kv); err == nil {
					kv = v
				}
			}

			keyV := strings.SplitN(kv, "=", 2)
			if len(keyV[0]) == 0 || len(keyV[1]) == 0 {
				continue
			}
			appendKeyValues = append(appendKeyValues, keyV)
		}
	}

	if len(flag.Args()) > 0 {
		for _, fileName := range flag.Args() {
			if err := processFile(fileName); err != nil {
				panic(err)
			}
		}
	} else {
		processInput(os.Stdin)
	}

	globalMap.Range(func(key, value interface{}) bool {
		v := map[string]interface{}{
			aggsKey:   key,
			"size":    len(*value.(*[]map[string]interface{})),
			"records": *value.(*[]map[string]interface{}),
		}

		if len(appendKeyValues) > 0 {
			for _, kv := range appendKeyValues {
				if kv[1][0] == '"' {
					if v, err := strconv.Unquote(kv[1]); err == nil {
						kv[1] = v
					}
				}
				v[kv[0]] = replaceVariable(v, kv[1])
			}
		}

		if b, err := json.Marshal(v); err != nil {
			log.Println("[ERROR]", err)
			return false
		} else {
			fmt.Println(string(b))
		}

		return true
	})

}
