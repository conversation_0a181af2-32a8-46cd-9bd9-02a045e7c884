# Implement restful agent of sutra.

## start as dev
开发模式下启动服务

准备启动, 安装gf工具
```shell
wget -O gf https://github.com/gogf/gf/releases/latest/download/gf_$(go env GOOS)_$(go env GOARCH) && chmod +x gf && ./gf install -y && rm ./gf
```
明确Sutra API的位置, 通过修改<rest-agent/manifest/config/config.yaml>修改sutra的配置参数
```yaml
....
......
sutra:
  server: "http://127.0.0.1:2333"
  closeLocalEngine: true
  openCloudQuery: true
  OpenQueryJson: true
  OpenGzip: true
  OpenDebug: false
  HashCacheMinute: 10
  HashCacheFile:
....
...
```

```shell
go mod tidy
cd cmd/rest-agent
gf run cmd/main.go
```

## 正式启动
环境安装与开发环境一样
```shell
go mod tidy
cd cmd/rest-agent
```

命令
```shell
go run cmd/main.go
```