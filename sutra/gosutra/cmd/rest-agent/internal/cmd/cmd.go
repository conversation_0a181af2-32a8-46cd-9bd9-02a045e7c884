package cmd

import (
	"context"
	"fmt"
	"github.com/pkg/errors"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"

	"git.gobies.org/sutra/gosutra/cmd/rest-agent/internal/controller"
)

func Init() {
	ctx := context.Background()
	data, err := g.Cfg().Get(ctx, fmt.Sprintf(cfgSutraPrefix))
	if err != nil {
		panic(errors.Wrapf(err, "can not init sutra config"))
	}
	controller.Init(data)
}

var (
	cfgSutraPrefix = "sutra"

	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			s := g.Server()
			s.Group("/", func(group *ghttp.RouterGroup) {
				group.Middleware(ghttp.MiddlewareHandlerResponse)
				group.Bind(
					controller.Product,
				)
			})
			s.Run()
			return nil
		},
	}
)
