package controller

import (
	"context"
	"encoding/json"
	"git.gobies.org/sutra/gosutra"
	"git.gobies.org/sutra/gosutra/algo"
	v1 "git.gobies.org/sutra/gosutra/cmd/rest-agent/api/v1"
	"git.gobies.org/sutra/gosutra/cmd/rest-agent/logger"
	"git.gobies.org/sutra/gosutra/structs"
	"git.gobies.org/sutra/gosutra/utils"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"
)

var (
	touched      = 0
	queryHandler = func(w http.ResponseWriter, r *http.Request) {
		getHash := func(val string) string {
			domHash, _ := utils.BodyHash2(val)
			return algo.SignString(domHash)
		}

		switch r.URL.Path {
		case "/api/v1/query", "/api/v1/querydetail":
			touched = 1

			var hashes []string
			err := json.NewDecoder(r.Body).Decode(&hashes)
			if err != nil {
				http.Error(w, err.Error(), http.StatusBadRequest)
				return
			}
			switch hashes[0] {
			case "31fd60f56a1dd3da17a586816e2df77f":
				// app no child
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
   "code": 200,
   "data": {
       "31fd60f56a1dd3da17a586816e2df77f": {
           "has_products": true,
           "products": [
               {
                   "hash":"31fd60f56a1dd3da17a586816e2df77f",
                   "hash_type": 0,
                   "product":"myapp"
               }
           ]
       }
   }
}
`))
				return
			case getHash(`<html><head></head><body><ede4a241dec55c7f96620edc64eb94ad></ede4a241dec55c7f96620edc64eb94ad></body></html>`):
				// has children
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
   "code": 200,
   "data": {
       "ede4a241dec55c7f96620edc64eb94ad": {
           "has_products": true,
           "products": [
               {
                   "hash":"ede4a241dec55c7f96620edc64eb94ad",
                   "hash_type": 0,
                   "product":"myapp",
                    "children": [
                        {
                            "product": "Apache-httpd",
                            "product_model": "",
                            "product_version": "v2.0.0",
                            "product_zh": "Apache服务器",
                            "product_url": "http://apache.com",
                            "level_code": 3,
                            "country_code": 2,
                            "soft_hard_code": 1
                        }
                    ]
               }
           ]
       }
   }
}
`))
				return
			case "abcb0ba9cefefbb3ad25f955e5ed2e05":
				// known hash 不是黑名单
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
   "code": 200,
   "data": {
       "abcb0ba9cefefbb3ad25f955e5ed2e05": {
            "has_products": false,
            "need_more_hash": false
       }
   }
}
`))
			case "f8f64cfbbbbac1db3b5939c27425675f":
				// known hash 是黑名单
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
   "code": 200,
   "data": {
       "f8f64cfbbbbac1db3b5939c27425675f": {
            "has_products": false,
            "need_more_hash": false,
			"hash_state": 2
       }
   }
}
`))
				return
			case getHash(`<html><head></head><body><unknown></unknown></body></html>`): //"b5569dd36789b7be89cad28bc3c8a495":
				// unknown hash
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
   "code": 200,
   "data": {
       "b5569dd36789b7be89cad28bc3c8a495": {
            "has_products": false,
            "need_more_hash": true
       }
   }
}
`))
				return
			case "97cf192300b888da473be27d5d84abe1":
				// unknown app hash
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
   "code": 200,
   "data": {
       "97cf192300b888da473be27d5d84abe1": {
            "has_products": false,
            "need_more_hash": true
       }
   }
}
`))
				return
			}

		}
	}
)

func Test_cHello_Products(t *testing.T) {
	logger.Init()
	ts := httptest.NewServer(http.HandlerFunc(queryHandler))
	defer ts.Close()

	testFilePath := "abc.txt"
	// 一条hash
	err := ioutil.WriteFile(testFilePath,
		[]byte(`[{"product":"Linksys-IP-Cameras","level":"1","category":"Cameras and Surveillance","parent_category":"Internet of Things (IoT) Device","softhard":"1","company":"","from":"cloud","cached_at":"2022-02-19 20:01:46","ehash":"c9a9e1e1c66f5a6931be4090fc7cbf75"}]`),
		0666)
	assert.Nil(t, err)
	defer os.Remove(testFilePath)

	client = gosutra.NewClient(gosutra.WithServer(ts.URL),
		gosutra.WithCloudQuery(true),
		gosutra.WithQueryRawJson(true),
		gosutra.WithHashCacheFile(testFilePath),
		gosutra.WithHashCacheMinute(10),
	)

	type args struct {
		ctx context.Context
		req *v1.ProductsReq
	}
	tests := []struct {
		name    string
		args    args
		wantRes *v1.ProductsRes
		wantErr error
	}{
		{
			"no-product-need-more-hash",
			args{
				ctx: nil,
				req: &v1.ProductsReq{Body: "<html><head></head><body><unknown></unknown></body></html>"},
			},
			func() *v1.ProductsRes {
				return &v1.ProductsRes{
					Products: []*v1.ProductDetail{},
				}
			}(),
			nil,
		},
		{
			"have-product-no-more-hash",
			args{
				ctx: nil,
				req: &v1.ProductsReq{Body: "<html><head></head><body><ede4a241dec55c7f96620edc64eb94ad></ede4a241dec55c7f96620edc64eb94ad></body></html>"},
			},
			func() *v1.ProductsRes {
				return &v1.ProductsRes{
					Products: func() []*v1.ProductDetail {
						return []*v1.ProductDetail{
							{
								Name:     "myapp",
								Ehash:    "ede4a241dec55c7f96620edc64eb94ad",
								Level:    "0",
								SoftHard: "0",
								From:     "cloud",
								CachedAt: int(time.Now().Unix()),
							},
							{
								Name:     "Apache-httpd",
								Ehash:    "ede4a241dec55c7f96620edc64eb94ad",
								Level:    "3",
								SoftHard: "0",
								From:     "cloud",
								CachedAt: int(time.Now().Unix()),
							},
						}
					}(),
				}
			}(),
			nil,
		},
		{
			"fail-no-body-nor-header-request",
			args{
				ctx: nil,
				req: &v1.ProductsReq{},
			},
			nil,
			structs.ErrSutraJsonObjData,
		},
		//{
		//	"fail-no-body-with-header-request",
		//	args{
		//		ctx: nil,
		//		req: &v1.ProductsReq{
		//			Header: "Host: www.baidu.com\nServer: Nginx-1.4",
		//		},
		//	},
		//	nil,
		//	structs.ErrSutraJsonObjData,
		//},
		{
			"from-local-cache",
			args{
				ctx: nil,
				req: &v1.ProductsReq{Body: "<html><head></head><body><2222211111222331112></2222211111222331112></body></html>"},
			},
			func() *v1.ProductsRes {
				return &v1.ProductsRes{
					Products: func() []*v1.ProductDetail {
						return []*v1.ProductDetail{
							{
								Name:           "Linksys-IP-Cameras",
								Ehash:          "c9a9e1e1c66f5a6931be4090fc7cbf75",
								Level:          "1",
								SoftHard:       "1",
								From:           "cloud",
								ParentCategory: "Internet of Things (IoT) Device",
								Category:       "Cameras and Surveillance",
								CachedAt: func() int {
									t, _ := time.ParseInLocation("2006-01-02 15:04:05", "2022-02-19 20:01:46", time.Local)
									return int(t.Unix())
								}(),
							},
						}
					}(),
				}
			}(),
			nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert := require.New(t)
			c := &cProduct{}
			gotRes, err := c.Products(tt.args.ctx, tt.args.req)
			if tt.wantErr != nil {
				assert.ErrorIs(err, tt.wantErr)
				return
			}
			assert.NoError(err)
			assert.Equal(tt.wantRes, gotRes)
		})
	}
}
