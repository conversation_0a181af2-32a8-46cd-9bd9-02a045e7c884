/*
./sutrazeek http*.log | jq 'select(.products) | .ip, .products'

*/
package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"git.gobies.org/sutra/gosutra"
	"git.gobies.org/sutra/gosutra/internal/genmap"
	"git.gobies.org/sutra/gosutra/internal/pipeinit"
	"log"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"unicode"
)

var sutraCli *gosutra.Client

// HTTP provides a data structure for entries in zeek's HTTP log file
type HTTP struct {
	// TimeStamp of this connection
	TimeStamp int64 `bson:"ts" bro:"ts" brotype:"time" json:"-"`
	// TimeStampGeneric is used when reading from json files
	TimeStampGeneric interface{} `bson:"-" json:"ts"`
	// UID is the Unique Id for this connection (generated by Bro)
	UID string `bson:"uid" bro:"uid" brotype:"string" json:"uid"`
	// Source is the source address for this connection
	Source string `bson:"id_orig_h" bro:"id.orig_h" brotype:"addr" json:"id.orig_h"`
	// SourcePort is the source port of this connection
	SourcePort int `bson:"id_orig_p" bro:"id.orig_p" brotype:"port" json:"id.orig_p"`
	// Destination is the destination of the connection
	Destination string `bson:"id_resp_h" bro:"id.resp_h" brotype:"addr" json:"id.resp_h"`
	// DestinationPort is the port at the destination host
	DestinationPort int `bson:"id_resp_p" bro:"id.resp_p" brotype:"port" json:"id.resp_p"`
	// Transdepth is the ordinal value of requests into a pipeline transaction
	TransDepth int64 `bson:"trans_depth" bro:"trans_depth" brotype:"count" json:"trans_depth"`
	// Version is the value of version in the request
	Version string `bson:"version" bro:"version" brotype:"string" json:"version"`
	// Method is the request method used
	Method string `bson:"method" bro:"method" brotype:"string" json:"method"`
	// Host is the value of the HOST header
	Host string `bson:"host" bro:"host" brotype:"string" json:"host"`
	// URI is the uri used in this request
	URI string `bson:"uri" bro:"uri" brotype:"string" json:"uri"`
	// Referrer is the value of the referrer header in the request
	Referrer string `bson:"referrer" bro:"referrer" brotype:"string" json:"referrer"`
	// UserAgent gives the user agent from the request
	UserAgent string `bson:"user_agent" bro:"user_agent" brotype:"string" json:"user_agent"`
	// ReqLen holds the length of the request body uncompressed
	ReqLen int64 `bson:"request_body_len" bro:"request_body_len" brotype:"count" json:"request_body_len"`
	// RespLen hodls the length of the response body uncompressed
	RespLen int64 `bson:"response_body_len" bro:"response_body_len" brotype:"count" json:"response_body_len"`
	// StatusCode holds the status result
	StatusCode int64 `bson:"status_code" bro:"status_code" brotype:"count" json:"status_code"`
	// StatusMsg contains a string status message returned by the server
	StatusMsg string `bson:"status_msg" bro:"status_msg" brotype:"string" json:"status_msg"`
	// InfoCode holds the last seen 1xx informational reply code
	InfoCode int64 `bson:"info_code" bro:"info_code" brotype:"count" json:"info_code"`
	// InfoMsg holds the last seen 1xx message string
	InfoMsg string `bson:"info_msg" bro:"info_msg" brotype:"string" json:"info_msg"`
	// Tags contains a set of indicators of various attributes related to a particular req and
	// response pair
	Tags []string `bson:"tags" bro:"tags" brotype:"set[enum]" json:"tags"`
	// UserName will contain a username in the case of basic auth implementation
	UserName string `bson:"username" bro:"username" brotype:"string" json:"username"`
	// Password will contain a password in the case of basic auth implementation
	Password string `bson:"password" bro:"password" brotype:"string" json:"password"`
	// Proxied contains all headers that indicate a request was proxied
	Proxied []string `bson:"proxied" bro:"proxied" brotype:"set[string]" json:"proxied"`
	// OrigFuids contains an ordered vector of uniq file IDs
	OrigFuids []string `bson:"orig_fuids" bro:"orig_fuids" brotype:"vector[string]" json:"orig_fuids"`
	// OrigFilenames contains an ordered vector of filenames from the client
	OrigFilenames []string `bson:"orig_filenames" bro:"orig_filenames" brotype:"vector[string]" json:"orig_filenames"`
	// OrigMimeTypes contains an ordered vector of mimetypes
	OrigMimeTypes []string `bson:"orig_mime_types" bro:"orig_mime_types" brotype:"vector[string]" json:"orig_mime_types"`
	// RespFuids contains an ordered vector of unique file IDs in the response
	RespFuids []string `bson:"resp_fuids" bro:"resp_fuids" brotype:"vector[string]" json:"resp_fuids"`
	// RespFilenames contains an ordered vector of unique files in the response
	RespFilenames []string `bson:"resp_filenames" bro:"resp_filenames" brotype:"vector[string]" json:"resp_filenames"`
	// RespMimeTypes contains an ordered vector of unique MIME entities in the HTTP response body
	RespMimeTypes []string `bson:"resp_mime_types" bro:"resp_mime_types" brotype:"vector[string]" json:"resp_mime_types"`
	// AgentHostname names which sensor recorded this event. Only set when combining logs from multiple sensors.
	AgentHostname string `bson:"agent_hostname" bro:"agent_hostname" brotype:"string" json:"agent_hostname"`
	// AgentUUID identifies which sensor recorded this event. Only set when combining logs from multiple sensors.
	AgentUUID string `bson:"agent_uuid" bro:"agent_uuid" brotype:"string" json:"agent_uuid"`

	Body               string   `bson:"body" bro:"body" brotype:"string" json:"body"`
	HeaderHost         string   `bson:"header_host" bro:"header_host" brotype:"string" json:"header_host"`
	ServerHeaderNames  []string `bson:"server_header_names" bro:"server_header_names" brotype:"string" json:"server_header_names"`
	ServerHeaderValues []string `bson:"server_header_values" bro:"server_header_values" brotype:"string" json:"server_header_values"`
}

// headerCap 大小写转换
func headerCap(v string) string {
	v = strings.ToLower(v)
	vs := strings.Split(v, "-")
	var ret string
	for i := range vs {
		if i != 0 {
			ret += "-"
		}
		ret += fmt.Sprintf("%c%s", byte(unicode.ToUpper(rune(vs[i][0]))), vs[i][1:])
	}
	return ret
}

func processLine(line string) bool {
	var v HTTP
	err := json.Unmarshal([]byte(line), &v)
	if err != nil {
		panic(err)
	}

	var serverLines string
	for i := range v.ServerHeaderNames {
		serverLines += fmt.Sprintf("%s: %s\r\n", headerCap(v.ServerHeaderNames[i]), v.ServerHeaderValues[i])
	}

	s := time.Now()
	httpInfo := map[string]interface{}{
		"timestamp":   s.String(),
		"ip":          v.Destination,
		"port":        v.DestinationPort,
		"client_ip":   v.Source,
		"client_port": v.SourcePort,
		"protocol":    "http",
		"header":      fmt.Sprintf("HTTP/%s %d %s\r\n%s\r\n", v.Version, v.StatusCode, v.StatusMsg, serverLines),
		"body":        v.Body,
		//"server": p.resp.Server,
		"host": v.Host,
		"url":  v.URI,
	}

	if v := genmap.GenProductsInfoFromMap(httpInfo, sutraCli, nil, s); v != nil {
		fmt.Println(string(v))
	}

	return true
}

var processed int32

func worker(wg *sync.WaitGroup, jobs <-chan *string) {
	defer wg.Done()
	for j := range jobs {
		processLine(*j)
		log.Printf("--- %d", atomic.AddInt32(&processed, 1))
	}
}

func main() {
	var localEngine = flag.Bool("localEngine", true, "enable local engine or not")
	var workerNumber = flag.Int("workerNumber", 10, "worker pool number")

	flag.Parse()

	if *localEngine {
		gosutra.LoadInnerRules()
	}

	sutraCli = gosutra.NewClient(gosutra.WithCloudQuery(true),
		gosutra.WithQueryRawJson(false),
		gosutra.WithServer("http://api.sutrad.org"),
		gosutra.WithHashCacheMinute(10))

	numJobs := *workerNumber * 2
	jobs := make(chan *string, numJobs)
	var wg sync.WaitGroup
	for w := 1; w <= *workerNumber; w++ {
		wg.Add(1)
		go worker(&wg, jobs)
	}

	i := 0
	pipeinit.ProcessFileOrInput(flag.Args(), func(line string) bool {
		i++
		log.Printf("+++ %d", i)
		jobs <- &line
		return true
	})

	close(jobs)
	wg.Wait()
}
