package pipeinit

import (
	"bufio"
	"io"
	"log"
	"os"
)

func processInput(reader io.Reader, onLine func(line string) bool) bool {
	scanner := bufio.NewScanner(reader)
	scanner.Buffer([]byte{}, bufio.MaxScanTokenSize*1024)
	for scanner.Scan() { // internally, it advances token based on sperator
		line := scanner.Text()
		if len(line) > 0 {
			if !onLine(line) {
				break
			}
		}
	}

	if err := scanner.Err(); err != nil {
		log.Println(err)
		return false
	}

	return true
}

func processFile(fileName string, onLine func(line string) bool) error {
	f, err := os.Open(fileName)
	if err != nil {
		return err
	}
	defer f.Close()
	processInput(f, onLine)
	return nil
}

/*
ProcessFileOrInput 处理文件列表或者管道
如果有文件名就处理文件，如果没有就相当于管道输入

	pipeinit.ProcessFileOrInput(flag.Args(), func(line string) bool {
		return true
	})
*/
func ProcessFileOrInput(files []string, onLine func(line string) bool) {
	if len(files) > 0 {
		for _, fileName := range files {
			if err := processFile(fileName, onLine); err != nil {
				panic(err)
			}
		}
	} else {
		processInput(os.Stdin, onLine)
	}
}
