package cloudengine

type Client struct {
	EnableQueryRawJson      bool   // 是否开启查询原始json模式
	Server                  string // 服务器地址
	Debug                   bool   // 调试开关
	EnableGzipWhenLargeJson bool   // 是否在大文件的情况下开启Gzip
}

type ClientOption func(*Client)

/*
NewClient 创建客户端
*/
func NewClient(options ...ClientOption) *Client {
	c := &Client{
		Server: "http://api.sutrad.org",
	}
	for _, opt := range options {
		opt(c)
	}
	return c
}

// 控制请求服务器地址
func WithServer(server string) ClientOption {
	return func(c *Client) {
		c.Server = server
	}
}

// 控制是否开启查询原始json模式开关
func WithQueryRawJson(v bool) ClientOption {
	return func(c *Client) {
		c.EnableQueryRawJson = v
	}
}

// 控制是否开启gzip模式，大文件模式下可以减少网络传输
func WithOpenGzip(v bool) ClientOption {
	return func(c *Client) {
		c.EnableGzipWhenLargeJson = v
	}
}

// 控制是否开启调试
func WithDebug(v bool) ClientOption {
	return func(c *Client) {
		c.Debug = v
	}
}
