package cloudengine

import (
	"encoding/json"
	"errors"
	"log"
)

const (
	apiVersion             = "/api/v1"
	searchProductUrl       = apiVersion + "/query"
	searchProductDetailUrl = apiVersion + "/querydetail"
	submitGrabHashUrl      = apiVersion + "/queryhash"
	submitGrabInfoUrl      = apiVersion + "/queryinfo"
	searchIPUrl            = apiVersion + "/queryip"
)

type queryResp struct {
	Code  int64                    `json:"code"`
	Datas map[string]*hashInfoResp `json:"data"`
	Msg   string                   `json:"msg"`
}

type ipInfo struct {
	IP             string   `json:"ip"`
	Ports          []string `json:"ports"`
	Domains        []string `json:"domains"`
	Protocols      []string `json:"protocols"`
	Products       []string `json:"products"`
	Categories     []string `json:"categories"`
	LastUpdateTime string   `json:"last_update_time"`
}

// queryIPResp 和 queryIPRespVerbose
type queryIPResp struct {
	Code  int64  `json:"code"`
	Datas ipInfo `json:"data"`
	Msg   string `json:"msg"`
}

type queryHashResp struct {
	Code  int64 `json:"code"`
	Datas map[string]struct {
		NeedMoreInfo bool `json:"need_more_info"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type hashInfoResp struct {
	HasProducts  bool       `json:"has_products"`
	NeedMoreHash bool       `json:"need_more_hash,omitempty"`
	State        int        `json:"hash_state,omitempty"`
	Products     []*Product `json:"products"`
}

type Product struct {
	Hash           string `json:"hash,omitempty"`
	HashType       int64  `json:"hash_type,omitempty"`
	Product        string `json:"product,omitempty"`
	ProductModel   string `json:"product_model,omitempty"`
	ProductVersion string `json:"product_version,omitempty"`
	ProductURL     string `json:"product_url,omitempty"`
	LevelCode      int    `json:"level_code,omitempty"`
	CountryCode    int    `json:"country_code,omitempty"`
	SoftHardCode   int    `json:"soft_hard_code,omitempty"`
	Category       string `json:"category,omitempty"`
	ParentCategory string `json:"parent_category,omitempty"`
	Children       []struct {
		Product        string `json:"product,omitempty"`
		ProductModel   string `json:"product_model,omitempty"`
		ProductVersion string `json:"product_version,omitempty"`
		ProductZh      string `json:"product_zh,omitempty"`
		ProductURL     string `json:"product_url,omitempty"`
		LevelCode      int    `json:"level_code,omitempty"`
		CountryCode    int    `json:"country_code,omitempty"`
		SoftHardCode   int    `json:"soft_hard_code,omitempty"`
		Category       string `json:"category,omitempty"`
		ParentCategory string `json:"parent_category,omitempty"`
	} `json:"children,omitempty"`
}

//queryHashReq
//hash_type:0:body,1:banner,2:header,3:server,4:title
type queryHashReq struct {
	Hash           string `json:"hash"`
	HashType       int    `json:"hash_type"`
	NormalizedHash string `json:"normalized_hash"`
}

type grabInfoReq struct {
	Data           string `json:"data"`
	Hash           string `json:"hash"`
	NormalizedHash string `json:"normalized_hash"`
	HashType       int    `json:"hash_type"`
}

//queryProduct 查询产品信息
func (c *Client) queryProduct(hashs []string, detail bool) (res map[string]*hashInfoResp, err error) {
	var (
		resData []byte
		resp    = &queryResp{}
		reqUrl  = searchProductUrl
	)
	if len(hashs) <= 0 {
		return nil, nil
	}
	if detail {
		reqUrl = searchProductDetailUrl
	}
	// for _,hash := range hashs {
	// 	sendData = append(sendData, hash)
	// }
	resData, err = c.postWithRawData(c.Server+reqUrl, map[string]string{}, hashs)
	if err != nil {
		return nil, err
	}
	if c.Debug {
		log.Println(string(resData))
	}

	err = json.Unmarshal(resData, &resp)
	if err != nil {
		return nil, err
	}
	if resp.Code != 200 {
		return nil, errors.New(resp.Msg)
	}
	return resp.Datas, nil
}

//moreHash 提交原始hash
func (c *Client) moreHash(data []queryHashReq) (res []string, err error) {
	var (
		resData []byte
		resp    = queryHashResp{}
	)

	resData, err = c.postWithRawData(c.Server+submitGrabHashUrl, map[string]string{}, data)
	if err != nil {
		return nil, err
	}
	if c.Debug {
		log.Println("第二次:", string(resData))
	}

	err = json.Unmarshal(resData, &resp)
	if err != nil {
		return nil, err
	}

	if resp.Code != 200 {
		return nil, errors.New(resp.Msg)
	}
	for hash, val := range resp.Datas {
		if val.NeedMoreInfo {
			res = append(res, hash)
		}
	}
	return
}

//grabInfoQuery 提交原始的数据
func (c *Client) grabInfoQuery(data []grabInfoReq) (res map[string]*hashInfoResp, err error) {
	var (
		resData []byte
		resp    = &queryResp{}
	)

	resData, err = c.postWithRawData(c.Server+submitGrabInfoUrl, map[string]string{}, data)
	if err != nil {
		return
	}
	if c.Debug {
		log.Println("第三次:", string(resData))
	}

	err = json.Unmarshal(resData, &resp)
	if err != nil {
		return
	}
	if resp.Code != 200 {
		err = errors.New(resp.Msg)
		return
	}

	return resp.Datas, nil
}

//queryIP 查询IP信息
func (c *Client) queryIP(ip string, detail bool) (res *queryIPResp, err error) {
	var (
		resData []byte
		resp    = &queryIPResp{}
		reqUrl  = searchIPUrl
	)
	if len(ip) <= 0 {
		return nil, nil
	}

	if detail {
		reqUrl = searchIPUrl
	}

	resData, err = c.get(c.Server+reqUrl+"?ip="+ip, map[string]string{})
	if err != nil {
		return nil, err
	}
	if c.Debug {
		log.Println(string(resData))
	}

	err = json.Unmarshal(resData, &resp)
	if err != nil {
		return nil, err
	}
	if resp.Code != 200 {
		return nil, errors.New(resp.Msg)
	}
	return resp, nil
}
