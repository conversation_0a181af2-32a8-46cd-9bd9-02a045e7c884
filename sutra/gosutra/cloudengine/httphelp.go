package cloudengine

import (
	"bytes"
	"compress/gzip"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
)

var ()

// postWithRawData post
func (c *Client) postWithRawData(path string, header map[string]string, sendData interface{}) (resData []byte, err error) {
	var (
		data []byte
	)
	data, err = json.Marshal(sendData)
	if err != nil {
		return
	}

	gzipped := false
	var buf *bytes.Buffer
	if len(data) > 1000 && c.EnableGzipWhenLargeJson {
		var b bytes.Buffer
		g := gzip.NewWriter(&b)
		if _, err = g.Write(data); err != nil {
			return
		}
		if err = g.Close(); err != nil {
			return
		}
		buf = &b
		gzipped = true
	} else {
		buf = bytes.NewBuffer(data)
	}

	req, err := http.NewRequest(http.MethodPost, path, buf)
	if err != nil {
		return
	}
	if header != nil {
		for k, v := range header {
			req.Header.Set(k, v)
		}
	}
	if gzipped {
		req.Header.Set("Content-Encoding", "gzip")
	}

	client := &http.Client{}
	client.Transport = &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	resp, err := client.Do(req)
	if err != nil {
		return
	}
	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("invalid status code:%d", resp.StatusCode)
		return
	}
	defer resp.Body.Close()
	resData, err = ioutil.ReadAll(resp.Body)

	return
}

// postWithRawData post
func (c *Client) get(path string, header map[string]string) (resData []byte, err error) {

	req, err := http.NewRequest(http.MethodGet, path, nil)
	if err != nil {
		return
	}
	if header != nil {
		for k, v := range header {
			req.Header.Set(k, v)
		}
	}

	client := &http.Client{}
	client.Transport = &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	resp, err := client.Do(req)
	if err != nil {
		return
	}
	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("invalid status code:%d", resp.StatusCode)
		return
	}
	defer resp.Body.Close()
	resData, err = ioutil.ReadAll(resp.Body)

	return
}
