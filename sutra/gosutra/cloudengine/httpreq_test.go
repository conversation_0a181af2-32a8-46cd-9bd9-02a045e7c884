package cloudengine

import (
	"encoding/json"
	"git.gobies.org/sutra/gosutra/structs"
	"github.com/stretchr/testify/assert"
	"net/http"
	"net/http/httptest"
	"testing"
)

func queryHander(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path == "/api/v1/query" || r.URL.Path == "/api/v1/querydetail" {
		var hashes []string
		err := json.NewDecoder(r.Body).Decode(&hashes)
		if err != nil {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}

		magicHash := false
		for _, hash := range hashes {
			switch hash {
			case "31fd60f56a1dd3da17a586816e2df77f", "abcb0ba9cefefbb3ad25f955e5ed2e05", "b5569dd36789b7be89cad28bc3c8a495", "f8f64cfbbbbac1db3b5939c27425675f":
				magicHash = true
			}
		}
		if magicHash {
			var ret queryResp
			ret.Code = 200
			ret.Datas = make(map[string]*hashInfoResp)
			for _, hash := range hashes {
				switch hash {
				case "31fd60f56a1dd3da17a586816e2df77f":
					pData := &Product{
						HashType: 0,
						Hash:     hash,
						Product:  "Hikvision",
					}
					if r.URL.Path == "/api/v1/querydetail" {
						pData.SoftHardCode = 2
						pData.LevelCode = 1
					}

					ret.Datas[hash] = &hashInfoResp{
						HasProducts:  true,
						NeedMoreHash: false,
						Products: []*Product{
							pData,
						},
					}
				case "abcb0ba9cefefbb3ad25f955e5ed2e05":
					// known hash
					ret.Datas[hash] = &hashInfoResp{
						HasProducts:  false,
						NeedMoreHash: false,
					}
				case "f8f64cfbbbbac1db3b5939c27425675f":
					// known black hash
					ret.Datas[hash] = &hashInfoResp{
						HasProducts:  false,
						NeedMoreHash: false,
						State:        2,
					}
				case "b5569dd36789b7be89cad28bc3c8a495":
					// unknown hash
					ret.Datas[hash] = &hashInfoResp{
						HasProducts:  false,
						NeedMoreHash: true,
					}
				}
			}
			d, _ := json.Marshal(ret)
			w.WriteHeader(http.StatusOK)
			w.Write(d)
		} else {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
   "code": 200,
   "data": {
       "15290ab0cf39fc2d32298230119d9820": {
           "has_products": true,
           "products": [
               {
                   "hash":"15290ab0cf39fc2d32298230119d9820",
                   "hash_type": 0,
                   "product":"Hikvision",
                   "product_zh":"海康威视"
               }
           ]
       },
       "193cf2980dab3cdd137903298abc3211": {
           "has_products": false,
           "need_more_hash": false
       },
       "62cc124096533ad202f34a5a678ca3f1": {
           "has_products": false,
           "need_more_hash": true
       }
   }
}
`))
		}
	}
}

func TestClient_ProductsOfHashes(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(queryHander))
	defer ts.Close()

	c := NewClient(WithServer(ts.URL))
	r, err := c.ProductsOfHashes([]string{"15290ab0cf39fc2d32298230119d9820", "193cf2980dab3cdd137903298abc3211", "62cc124096533ad202f34a5a678ca3f1"})
	assert.Nil(t, err)
	ps := r.Get("15290ab0cf39fc2d32298230119d9820")
	assert.Equal(t, 1, len(ps))
	assert.Equal(t, "Hikvision", ps[0].Name)
	ps = r.Get("62cc124096533ad202f34a5a678ca3f1")
	assert.Equal(t, 0, len(ps))

	var sutraHash map[string]*hashInfoResp
	// 已知有产品
	obj, _ := structs.NewJsonObj(`{"body":"<html><head></head><body><myapp></myapp></body></html>"}`)
	assert.Equal(t, "31fd60f56a1dd3da17a586816e2df77f", obj.EHash())
	sutraHash, err = c.queryProduct([]string{obj.EHash()}, true)
	assert.Nil(t, err)
	assert.False(t, sutraHash[obj.EHash()].NeedMoreHash)

	// 已知没有产品
	obj, _ = structs.NewJsonObj(`{"body":"<html><head></head><body><known></known></body></html>"}`)
	assert.Equal(t, "abcb0ba9cefefbb3ad25f955e5ed2e05", obj.EHash())
	sutraHash, err = c.queryProduct([]string{obj.EHash()}, true)
	assert.Nil(t, err)
	assert.False(t, sutraHash[obj.EHash()].NeedMoreHash)

	// 已知黑名单
	obj, _ = structs.NewJsonObj(`{"body":"<html><head></head><body><known><black></black></known></body></html>"}`)
	assert.Equal(t, "f8f64cfbbbbac1db3b5939c27425675f", obj.EHash())
	sutraHash, err = c.queryProduct([]string{obj.EHash()}, true)
	assert.Nil(t, err)
	assert.False(t, sutraHash[obj.EHash()].NeedMoreHash)

	// 未知
	obj, _ = structs.NewJsonObj(`{"body":"<html><head></head><body><unknown></unknown></body></html>"}`)
	assert.Equal(t, "b5569dd36789b7be89cad28bc3c8a495", obj.EHash())
	sutraHash, err = c.queryProduct([]string{obj.EHash()}, true)
	if err != nil {
		return
	}
	assert.True(t, sutraHash[obj.EHash()].NeedMoreHash)
}
