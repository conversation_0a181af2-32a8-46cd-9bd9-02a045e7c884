package cloudengine

import (
	"git.gobies.org/sutra/gosutra/structs"
	"log"
	"strconv"
	"sync/atomic"
	"time"
)

var (
	backgroundRunningCount int32 = 0 //后台运行的数量
)

const (
	BLOCKED = "-"
)

type CallbackFn func(*structs.JsonObj, []*structs.Product)

// buildObjsFromEHashes 根据原始objs匹配需要保留的ehash的列表，生成新的列表
func buildObjsFromEHashes(rawobjs []*structs.JsonObj, keepEHashes []string) []*structs.JsonObj {
	if len(keepEHashes) < 1 || len(rawobjs) < 1 {
		return nil
	}

	var newObjs []*structs.JsonObj
	for _, ehash := range keepEHashes {
		for _, obj := range rawobjs {
			if obj.EHash() == ehash {
				newObjs = append(newObjs, obj)
				break
			}
		}
	}
	return newObjs
}

// 将obj的type转换成api的type
func objApiType(obj *structs.JsonObj) int {
	stype := 0
	switch obj.Type() {
	case structs.OtSubdomain:
	case structs.OtService:
		stype = 1
	}
	return stype
}

func (c *Client) doMore(objs []*structs.JsonObj, callback CallbackFn) {
	atomic.AddInt32(&backgroundRunningCount, 1)
	defer atomic.AddInt32(&backgroundRunningCount, -1)

	// 构建提交的数据结构
	var reqs []queryHashReq
	for _, obj := range objs {
		reqs = append(reqs, queryHashReq{
			Hash:           obj.EHash(),
			HashType:       objApiType(obj),
			NormalizedHash: obj.NHash(),
		})
	}

	grabHashs, err := c.moreHash(reqs)
	if err != nil {
		log.Println("more hash request failed:", err)
		return
	}
	if !c.EnableQueryRawJson {
		return
	}

	objs = buildObjsFromEHashes(objs, grabHashs)

	// 构造需要提交json的数据结构
	var submitGrabInfoReqs []grabInfoReq
	for _, obj := range objs {
		submitGrabInfoReqs = append(submitGrabInfoReqs, grabInfoReq{
			Data:           obj.String(),
			Hash:           obj.EHash(),
			NormalizedHash: obj.NHash(),
			HashType:       objApiType(obj),
		})
	}

	if len(submitGrabInfoReqs) < 1 {
		return
	}

	sutraHash, err := c.grabInfoQuery(submitGrabInfoReqs)
	if err != nil {
		log.Println("grab info query failed", err)
		return
	}

	// 回调通知
	if callback != nil {
		for ehash, v := range sutraHash {
			if v.HasProducts {
				for _, o := range objs {
					if o.EHash() == ehash {
						callback(o, fillProducts(v.Products))
					}
				}
			}
		}
	}
}

func fillProducts(psv []*Product) []*structs.Product {
	var ps []*structs.Product
	for _, p := range psv {
		ps = append(ps, &structs.Product{
			Name:           p.Product,
			Level:          strconv.FormatInt(int64(p.LevelCode), 10),
			Category:       p.Category,
			ParentCategory: p.ParentCategory,
			SoftHard:       strconv.FormatInt(int64(p.SoftHardCode), 10),
			From:           "cloud",
			EHash:          p.Hash,
		})

		for _, cp := range p.Children {
			ps = append(ps, &structs.Product{
				Name:           cp.Product,
				Level:          strconv.FormatInt(int64(cp.LevelCode), 10),
				Category:       cp.Category,
				ParentCategory: cp.ParentCategory,
				SoftHard:       strconv.FormatInt(int64(p.SoftHardCode), 10),
				From:           "cloud",
				EHash:          p.Hash,
			})
		}
	}
	return ps
}

/*
ProductsOfHashes 批量查询，只查询原始ehash，不进行更多的处理

hashes 每一个都是ehash
*/
func (c *Client) ProductsOfHashes(hashes []string) (*HashResults, error) {
	sutraHash, err := c.queryProduct(hashes, true)
	if err != nil {
		return nil, err
	}

	var ps HashResults
	for ehash, v := range sutraHash {
		if v.HasProducts {
			if !ps.Add(ehash, fillProducts(v.Products)) {
				log.Println("[WARNING] hash exists:", ehash)
			}
		}
	}
	return &ps, nil
}

/*
Products 从json中获取产品列表

obj 是已经解析后的JsonObj
callback 是回调
*/
func (c *Client) Products(objs []*structs.JsonObj, callback CallbackFn) (res *HashResults, err error) {
	var ehashes []string

	// 提取hash列表
	for _, obj := range objs {
		body := obj.GetString("body")
		// 数字太小没有意义
		if len(body) < 10 {
			continue
		}

		ehashes = append(ehashes, obj.EHash())
	}
	if len(ehashes) < 1 {
		return
	}

	// 请求第一次
	var sutraHash map[string]*hashInfoResp
	sutraHash, err = c.queryProduct(ehashes, true)
	if err != nil {
		return
	}

	var ps HashResults
	var leftObjs []*structs.JsonObj
	for ehash, v := range sutraHash {
		if v.HasProducts {
			if !ps.Add(ehash, fillProducts(v.Products)) {
				log.Println("[WARNING] hash exists:", ehash)
			}
		} else if v.NeedMoreHash {
			// 服务器没有，需要提供更多数据
			for _, o := range objs {
				if o.EHash() == ehash {
					leftObjs = append(leftObjs, o)
				}
			}
		} else if v.State == 2 {
			// v.NeedMoreHash 为false说明是黑名单
			ps.Add(ehash, []*structs.Product{
				{
					EHash: ehash,
					Name:  BLOCKED,
				},
			})
		}
	}

	if len(leftObjs) > 0 {
		go c.doMore(leftObjs, callback)
	}

	return &ps, nil
}

// IPProducts 查询公网IP的产品列表
func (c *Client) IPProducts(ip string) (res []string, err error) {
	ipInfo, err := c.queryIP(ip, false)
	if err != nil {
		return nil, err
	}

	return ipInfo.Datas.Products, nil
}

// Wait 等待完成
func (c *Client) Wait() {
	for {
		if backgroundRunningCount == 0 {
			return
		}
		time.Sleep(time.Second)
	}
}
