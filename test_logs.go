package main

import (
	"context"
	"data_analysis/internal/config"
	"data_analysis/internal/sutra"
	"go-micro.dev/v4/logger"
	"log"
)

// 简单的测试程序，用于验证日志功能
func main() {
	log.Println("开始测试规则加载日志功能...")
	
	// 初始化配置
	cc := &config.Config{
		ApiHost: "http://localhost:8080", // 测试用的API地址
	}
	
	// 测试 Sutra 初始化
	log.Println("=== 测试 Sutra 初始化 ===")
	s, err := sutra.NewSutra()
	if err != nil {
		logger.Fatalf("初始化 Sutra 失败: %v", err)
	}
	
	// 测试添加规则
	log.Println("=== 测试添加规则 ===")
	testRule := `{"product":"test-product","rule":"banner=test-banner","rule_id":"test-001"}`
	totalRules, err := s.Add(context.Background(), testRule)
	if err != nil {
		logger.Errorf("添加测试规则失败: %v", err)
	} else {
		logger.Infof("测试规则添加成功，当前总规则数: %d", totalRules)
	}
	
	// 测试更新规则
	log.Println("=== 测试更新规则 ===")
	updateRule := `{"product":"test-product-updated","rule":"banner=test-banner-updated","rule_id":"test-001"}`
	err = s.Update(context.Background(), updateRule)
	if err != nil {
		logger.Errorf("更新测试规则失败: %v", err)
	} else {
		logger.Info("测试规则更新成功")
	}
	
	// 测试删除规则
	log.Println("=== 测试删除规则 ===")
	deleteRule := `{"rule_id":"test-001"}`
	remainingRules, err := s.Delete(context.Background(), deleteRule)
	if err != nil {
		logger.Errorf("删除测试规则失败: %v", err)
	} else {
		logger.Infof("测试规则删除成功，剩余规则数: %d", remainingRules)
	}
	
	log.Println("规则加载日志功能测试完成！")
}
