package handler

import (
	"bytes"
	"context"
	"data_analysis/internal/config"
	"data_analysis/internal/job"
	"data_analysis/internal/sutra"
	"encoding/json"
	"errors"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	"git.gobies.org/shared-platform/foscan/pkg/model"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4"
	"go-micro.dev/v4/client"
	"go-micro.dev/v4/logger"
	"sync"
)

type DataAnalysis struct {
	conf       *config.Config
	jobs       map[string]int64
	remainJobs map[string]int64
	lock       sync.Mutex
	srv        micro.Service
	sutra      sutra.Sutra
}

func NewDataAnalysis(conf *config.Config, srv micro.Service, sutra sutra.Sutra) DataAnalysis {
	logger.Info("[HANDLER] 开始初始化 DataAnalysis，准备加载外部规则...")

	rules, err := GetRules(conf.ApiHost)
	if err != nil {
		logger.Warnf("[HANDLER] 从 API 获取规则失败: %v", err)
	} else {
		logger.Infof("[HANDLER] 成功从 API 获取到 %d 条规则", len(rules))
	}

	successCount := 0
	failCount := 0

	if err == nil && len(rules) > 0 {
		logger.Info("[HANDLER] 开始逐条添加外部规则...")
		for i, r := range rules {
			var buf bytes.Buffer
			err = json.NewEncoder(&buf).Encode(r)
			if err != nil {
				logger.Warnf("[HANDLER] 第 %d 条规则 JSON 编码失败 (RuleID: %s): %v", i+1, r.RuleID, err)
				failCount++
				continue
			}

			totalRules, err := sutra.Add(context.Background(), buf.String())
			if err != nil {
				logger.Warnf("[HANDLER] 第 %d 条规则添加失败 (RuleID: %s): %v", i+1, r.RuleID, err)
				failCount++
			} else {
				logger.Infof("[HANDLER] 第 %d 条规则添加成功 (RuleID: %s, Product: %s), 当前总规则数: %d",
					i+1, r.RuleID, r.Product, totalRules)
				successCount++
			}
		}

		logger.Infof("[HANDLER] 外部规则加载完成 - 成功: %d 条, 失败: %d 条, 总计: %d 条",
			successCount, failCount, len(rules))
	} else if len(rules) == 0 {
		logger.Info("[HANDLER] 没有获取到外部规则")
	}

	return DataAnalysis{
		conf:       conf,
		srv:        srv,
		jobs:       make(map[string]int64),
		remainJobs: make(map[string]int64),
		sutra:      sutra,
	}
}

func (d *DataAnalysis) Increment(taskId string) {
	d.lock.Lock()
	defer d.lock.Unlock()

	if count, ok := d.jobs[taskId]; ok {
		d.jobs[taskId] = count + 1
	} else {
		d.jobs[taskId] = int64(1)
	}

	if count, ok := d.remainJobs[taskId]; ok {
		d.remainJobs[taskId] = count + 1
	} else {
		d.remainJobs[taskId] = int64(1)
	}

	logger.Infof("the DataAnalysis jobs total:%d remain:%d", d.jobs[taskId], d.remainJobs[taskId])
}

func (d *DataAnalysis) Decrement(taskId string) {
	d.lock.Lock()
	defer d.lock.Unlock()

	if count, ok := d.remainJobs[taskId]; ok {
		remain := count - 1
		d.remainJobs[taskId] = remain
		logger.Info("the DataAnalysis remain jobs:", remain)
	}
}

func (d *DataAnalysis) StateQuery(ctx context.Context, req *rpcx.StateQueryRequest, rsp *rpcx.StateQueryResponse) error {
	logger.Infof("Received DataAnalysis.StateQuery request: %v", req)

	if count, ok := d.jobs[req.TaskId]; ok {
		rsp.Total = count
	}

	if remain, ok := d.remainJobs[req.TaskId]; ok {
		rsp.Remain = remain
	}

	return nil
}

func (d *DataAnalysis) FinishedNotify(ctx context.Context, req *rpcx.FinishedNotifyRequest, rsp *rpcx.FinishedNotifyResponse) error {
	logger.Infof("Received DataAnalysis.FinishedNotify request: %v", req)

	d.lock.Lock()
	defer d.lock.Unlock()

	delete(d.jobs, req.TaskId)
	delete(d.remainJobs, req.TaskId)

	return nil
}

func (d *DataAnalysis) CustomRuleAdd(ctx context.Context, req *rpcx.CustomRuleRequest, rsp *rpcx.RuleResponse) error {
	logger.Infof("[HANDLER] 接收到自定义规则添加请求 - RuleID: %s, Product: %s", req.RuleId, req.Product)

	rule := model.Rule{
		RuleID:         req.RuleId,
		Product:        req.Product,
		Rule:           req.Rule,
		Level:          req.Level,
		Softhard:       req.Softhard,
		Company:        req.Company,
		Category:       req.Category,
		ParentCategory: req.ParentCategory,
		From:           req.From,
	}

	var buf bytes.Buffer
	err := json.NewEncoder(&buf).Encode(rule)
	if err != nil {
		logger.Warnf("[HANDLER] 自定义规则 JSON 编码失败 (RuleID: %s): %v", req.RuleId, err)
		return err
	}

	logger.Infof("[HANDLER] 开始添加自定义规则 (RuleID: %s, Product: %s, Rule: %s)",
		req.RuleId, req.Product, req.Rule)

	totalRules, err := d.sutra.Add(context.Background(), buf.String())
	if err != nil {
		logger.Warnf("[HANDLER] 自定义规则添加失败 (RuleID: %s): %v", req.RuleId, err)
		return err
	}

	logger.Infof("[HANDLER] 自定义规则添加成功 - RuleID: %s, Product: %s, 当前总规则数: %d",
		req.RuleId, req.Product, totalRules)
	return nil
}

func (d *DataAnalysis) CustomRuleUpdate(ctx context.Context, req *rpcx.CustomRuleRequest, rsp *rpcx.RuleResponse) error {
	logger.Infof("[HANDLER] 接收到自定义规则更新请求 - RuleID: %s, Product: %s", req.RuleId, req.Product)

	rule := model.Rule{
		RuleID:         req.RuleId,
		Product:        req.Product,
		Rule:           req.Rule,
		Level:          req.Level,
		Softhard:       req.Softhard,
		Company:        req.Company,
		Category:       req.Category,
		ParentCategory: req.ParentCategory,
		From:           req.From,
	}

	var buf bytes.Buffer
	err := json.NewEncoder(&buf).Encode(rule)
	if err != nil {
		logger.Warnf("[HANDLER] 自定义规则更新 JSON 编码失败 (RuleID: %s): %v", req.RuleId, err)
		return err
	}

	logger.Infof("[HANDLER] 开始更新自定义规则 (RuleID: %s, Product: %s, Rule: %s)",
		req.RuleId, req.Product, req.Rule)

	err = d.sutra.Update(context.Background(), buf.String())
	if err != nil {
		logger.Warnf("[HANDLER] 自定义规则更新失败 (RuleID: %s): %v", req.RuleId, err)
		return err
	}

	logger.Infof("[HANDLER] 自定义规则更新成功 - RuleID: %s, Product: %s", req.RuleId, req.Product)
	return nil
}

func (d *DataAnalysis) CustomRuleDelete(ctx context.Context, req *rpcx.CustomRuleIDRequest, rsp *rpcx.RuleResponse) error {
	logger.Infof("[HANDLER] 接收到自定义规则删除请求 - RuleID: %s", req.RuleId)

	rule := model.Rule{
		RuleID: req.RuleId,
	}

	var buf bytes.Buffer
	err := json.NewEncoder(&buf).Encode(rule)
	if err != nil {
		logger.Warnf("[HANDLER] 自定义规则删除 JSON 编码失败 (RuleID: %s): %v", req.RuleId, err)
		return err
	}

	logger.Infof("[HANDLER] 开始删除自定义规则 (RuleID: %s)", req.RuleId)

	totalRules, err := d.sutra.Delete(context.Background(), buf.String())
	if err != nil {
		logger.Warnf("[HANDLER] 自定义规则删除失败 (RuleID: %s): %v", req.RuleId, err)
		return err
	}

	logger.Infof("[HANDLER] 自定义规则删除成功 - RuleID: %s, 剩余总规则数: %d", req.RuleId, totalRules)
	return nil
}

func (d *DataAnalysis) HandleEvent(ctx context.Context, event *rpcx.DataAnalysisEvent) error {
	logger.Infof("Received DataAnalysis.HandleEvent request: %v", event)

	if event.TaskInfo.TaskId == "" || event.JobId == "" {
		logger.Errorf("参数错误 event: %+v", event)
		return errors.New("参数错误")
	}

	d.Increment(event.TaskInfo.TaskId)

	defer func() {
		d.Decrement(event.TaskInfo.TaskId)
	}()

	j := job.NewJob(event, d.sutra)

	dataAnalysisEvent, err := j.Run(ctx)

	if err != nil {
		logger.Errorf("HandleEvent error: %v  event:%v", err, event)
		return err
	}

	quickStoreEvent := &rpcx.QuickStoreEvent{
		TaskInfo: event.TaskInfo,
		JobId:    event.JobId,
		Origin:   event.Origin,
		Data: &rpcx.QuickStoreEvent_Normal{
			Normal: dataAnalysisEvent.Normal,
		},
	}

	err = d.srv.Client().Publish(ctx, client.NewMessage(constant.ServiceQuickStore, quickStoreEvent))

	if err != nil {
		logger.Errorf("DataAnalysis.HandleEvent send error: %v  event:%v", err, quickStoreEvent)
	}

	logger.Infof("DataAnalysis.HandleEvent send successful.task_id: %s origin:%s ip: %s port: %d rule_tags: %v", event.TaskInfo.TaskId, event.Origin, quickStoreEvent.GetNormal().Ip, quickStoreEvent.GetNormal().Port, quickStoreEvent.GetNormal().RuleTags)
	return nil
}
