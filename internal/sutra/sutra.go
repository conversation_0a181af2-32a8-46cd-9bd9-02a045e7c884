package sutra

import (
	"context"
	_ "embed"
	"errors"
	"fmt"
	"git.gobies.org/sutra/gosutra"
	"git.gobies.org/sutra/gosutra/rulengine"
	"git.gobies.org/sutra/gosutra/rulengine/rsa"
	"git.gobies.org/sutra/gosutra/structs"
	"go-micro.dev/v4/logger"
	"time"
)

type MatchData struct {
	Body     string `json:"body,omitempty" dc:"http body"`
	SubBody  string `json:"subbody,omitempty" dc:"http subbody"`
	Cert     string `json:"cert,omitempty" dc:"http cert"`
	Title    string `json:"title,omitempty" dc:"http title"`
	Domain   string `json:"domain,omitempty" dc:"域名"`
	Header   string `json:"header,omitempty" dc:"http header"`
	IconHash string `json:"icon_hash,omitempty" dc:"图标hash值"`
	Banner   string `json:"banner,omitempty" dc:"协议banner"`
	Protocol string `json:"protocol,omitempty" dc:"协议名"`
	Server   string `json:"server,omitempty" dc:"server头信息"`
	Fid      string `json:"fid,omitempty" dc:"fid"`
}

type sutra struct {
	client *gosutra.Client
}

type Sutra interface {
	Match(ctx context.Context, data string) ([]*structs.Product, error)
	Add(ctx context.Context, data string) (int, error)
	Update(ctx context.Context, data string) error
	Delete(ctx context.Context, data string) (int, error)
}

func (s *sutra) Match(ctx context.Context, data string) ([]*structs.Product, error) {
	return s.client.Products(data)
}

func (s *sutra) Add(ctx context.Context, data string) (int, error) {
	logger.Infof("[SUTRA] 开始添加规则: %s", data)
	beforeCount := rulengine.RuleSize()

	ok := rulengine.Load(ctx, data, false)
	if !ok {
		logger.Errorf("[SUTRA] 规则添加失败: %s", data)
		return 0, errors.New(fmt.Sprintf("sutra add error:%s", data))
	}

	afterCount := rulengine.RuleSize()
	addedCount := afterCount - beforeCount
	logger.Infof("[SUTRA] 规则添加成功 - 新增规则数: %d, 总规则数: %d", addedCount, afterCount)

	return afterCount, nil
}

func (s *sutra) Update(ctx context.Context, data string) error {
	logger.Infof("[SUTRA] 开始更新规则: %s", data)
	ruleCount := rulengine.RuleSize()

	ok := rulengine.UpdateOne(ctx, data)
	if !ok {
		logger.Errorf("[SUTRA] 规则更新失败: %s", data)
		return errors.New(fmt.Sprintf("sutra update error:%s", data))
	}

	logger.Infof("[SUTRA] 规则更新成功 - 当前总规则数: %d", ruleCount)
	return nil
}

func (s *sutra) Delete(ctx context.Context, data string) (int, error) {
	logger.Infof("[SUTRA] 开始删除规则: %s", data)
	beforeCount := rulengine.RuleSize()

	ok := rulengine.DeleteOne(ctx, data)
	if !ok {
		logger.Errorf("[SUTRA] 规则删除失败: %s", data)
		return 0, errors.New(fmt.Sprintf("sutra delete error:%s", data))
	}

	afterCount := rulengine.RuleSize()
	deletedCount := beforeCount - afterCount
	logger.Infof("[SUTRA] 规则删除成功 - 删除规则数: %d, 剩余规则数: %d", deletedCount, afterCount)

	return afterCount, nil
}

//go:embed pub.pem
var PublicKey []byte

func NewSutra() (Sutra, error) {
	logger.Info("[SUTRA] 开始初始化 Sutra 规则引擎...")
	startTime := time.Now()

	err := rsa.RSA.SetPublicKey(string(PublicKey))
	if err != nil {
		logger.Errorf("[SUTRA] RSA 公钥设置失败: %v", err)
		return nil, err
	}
	logger.Info("[SUTRA] RSA 公钥设置成功")

	// 记录加载前的状态
	beforeRuleCount := rulengine.RuleSize()
	beforeProductCount := len(rulengine.AllProducts())
	logger.Infof("[SUTRA] 加载前状态 - 规则数量: %d, 产品数量: %d", beforeRuleCount, beforeProductCount)

	logger.Info("[SUTRA] 开始加载内置加密规则...")
	gosutra.LoadInnerEncryptRules()

	// 获取加载后的规则统计信息
	afterRuleCount := rulengine.RuleSize()
	afterProductCount := len(rulengine.AllProducts())
	addedRules := afterRuleCount - beforeRuleCount
	addedProducts := afterProductCount - beforeProductCount

	logger.Infof("[SUTRA] 内置规则加载完成 - 新增规则: %d, 新增产品: %d", addedRules, addedProducts)
	logger.Infof("[SUTRA] 当前总计 - 规则数量: %d, 产品数量: %d", afterRuleCount, afterProductCount)

	args := []gosutra.ClientOption{
		gosutra.WithCloudQuery(false),
		gosutra.WithCloseLocalEngine(false),
		gosutra.WithQueryRawJson(false),
		gosutra.WithDebug(true),
	}

	client := gosutra.NewClient(args...)

	duration := time.Since(startTime)
	logger.Infof("[SUTRA] Sutra 规则引擎初始化完成 - 总耗时: %v, 最终规则数量: %d, 产品数量: %d",
		duration, afterRuleCount, afterProductCount)

	return &sutra{
		client: client,
	}, nil
}
