package sutra

import (
	"bufio"
	"context"
	"encoding/base64"
	"fmt"
	"git.gobies.org/sutra/gosutra/resources"
	"git.gobies.org/sutra/gosutra/rulengine"
	"git.gobies.org/sutra/gosutra/rulengine/rsa"
	"git.gobies.org/sutra/gosutra/structs"
	"go-micro.dev/v4/logger"
	"strings"
	"time"
	"github.com/vmihailenco/msgpack/v5"
)

// RobustRuleLoader 健壮的规则加载器
type RobustRuleLoader struct {
	skipErrors    bool
	maxErrors     int
	errorCount    int
	successCount  int
	skippedCount  int
}

// NewRobustRuleLoader 创建健壮的规则加载器
func NewRobustRuleLoader() *RobustRuleLoader {
	return &RobustRuleLoader{
		skipErrors: true,  // 跳过错误行，不停止整个加载过程
		maxErrors:  100,   // 最大允许错误数
	}
}

// LoadInnerEncryptRulesRobust 健壮地加载内置加密规则
func (r *RobustRuleLoader) LoadInnerEncryptRulesRobust() error {
	logger.Info("[ROBUST_LOADER] 开始健壮加载内置加密规则...")
	startTime := time.Now()
	
	// 读取加密规则文件
	data, err := resources.ResFs.ReadFile("rules.json")
	if err != nil {
		return fmt.Errorf("读取规则文件失败: %v", err)
	}
	
	logger.Infof("[ROBUST_LOADER] 成功读取规则文件，大小: %d bytes", len(data))
	
	// 解密和加载规则
	err = r.loadEncryptProductsRobust(context.Background(), string(data))
	if err != nil {
		return fmt.Errorf("加载规则失败: %v", err)
	}
	
	duration := time.Since(startTime)
	logger.Infof("[ROBUST_LOADER] 规则加载完成 - 耗时: %v, 成功: %d, 跳过: %d, 错误: %d", 
		duration, r.successCount, r.skippedCount, r.errorCount)
	
	return nil
}

// loadEncryptProductsRobust 健壮地加载加密产品规则
func (r *RobustRuleLoader) loadEncryptProductsRobust(ctx context.Context, rulesContent string) error {
	// 分割加密内容
	items := strings.SplitN(rulesContent, ":", 2)
	if len(items) != 2 {
		return fmt.Errorf("加密规则格式错误，应为 'signature:base64_content'")
	}
	
	// Base64 解码
	encoded, err := base64.StdEncoding.DecodeString(items[1])
	if err != nil {
		return fmt.Errorf("Base64 解码失败: %v", err)
	}
	
	logger.Infof("[ROBUST_LOADER] Base64 解码成功，解码后大小: %d bytes", len(encoded))
	
	// RSA 公钥解密
	decryptContent, err := rsa.RSA.PubKeyDECRYPT(encoded)
	if err != nil {
		return fmt.Errorf("RSA 解密失败: %v", err)
	}
	
	logger.Infof("[ROBUST_LOADER] RSA 解密成功，解密后大小: %d bytes", len(decryptContent))
	
	// 逐行解析规则
	scanner := bufio.NewScanner(strings.NewReader(string(decryptContent)))
	lineNum := 0
	
	for scanner.Scan() {
		lineNum++
		
		// 检查是否需要停止
		select {
		case <-ctx.Done():
			logger.Warn("[ROBUST_LOADER] 加载被取消")
			return ctx.Err()
		default:
		}
		
		line := scanner.Bytes()
		if len(line) == 0 {
			r.skippedCount++
			continue
		}
		
		// 尝试添加规则
		if r.addProductEncryptLineRobust(string(line), lineNum) {
			r.successCount++
		} else {
			r.errorCount++
			
			// 检查是否超过最大错误数
			if r.errorCount >= r.maxErrors {
				logger.Errorf("[ROBUST_LOADER] 错误数量超过限制 (%d)，停止加载", r.maxErrors)
				return fmt.Errorf("错误数量超过限制: %d", r.maxErrors)
			}
		}
		
		// 每1000行输出一次进度
		if lineNum%1000 == 0 {
			logger.Infof("[ROBUST_LOADER] 处理进度: %d 行 (成功: %d, 错误: %d)", 
				lineNum, r.successCount, r.errorCount)
		}
	}
	
	if err := scanner.Err(); err != nil {
		return fmt.Errorf("扫描文件失败: %v", err)
	}
	
	logger.Infof("[ROBUST_LOADER] 文件扫描完成，总行数: %d", lineNum)
	return nil
}

// addProductEncryptLineRobust 健壮地添加加密产品规则
func (r *RobustRuleLoader) addProductEncryptLineRobust(line string, lineNum int) bool {
	defer func() {
		if rec := recover(); rec != nil {
			logger.Errorf("[ROBUST_LOADER] 第 %d 行处理时发生 panic: %v", lineNum, rec)
		}
	}()

	// 关键：不能使用 rulengine.Load，因为它内部调用的还是会失败的逻辑
	// 我们需要直接调用更底层的方法，或者解析 MessagePack 格式

	// 尝试解析 MessagePack 格式的规则
	success := r.parseAndAddEncryptRule(line, lineNum)
	if !success {
		if r.skipErrors {
			logger.Warnf("[ROBUST_LOADER] 第 %d 行规则解析失败，已跳过", lineNum)
			return false
		} else {
			logger.Errorf("[ROBUST_LOADER] 第 %d 行规则解析失败", lineNum)
			return false
		}
	}

	return true
}

// truncateString 截断字符串用于日志显示
func (r *RobustRuleLoader) truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// parseAndAddEncryptRule 解析并添加加密规则
func (r *RobustRuleLoader) parseAndAddEncryptRule(line string, lineNum int) bool {
	// 尝试解析 MessagePack 格式
	var product structs.Product
	err := msgpack.Unmarshal([]byte(line), &product)
	if err != nil {
		logger.Warnf("[ROBUST_LOADER] 第 %d 行 MessagePack 解析失败: %v", lineNum, err)
		return false
	}

	// 验证必要字段
	if product.Name == "" {
		logger.Warnf("[ROBUST_LOADER] 第 %d 行产品名称为空", lineNum)
		return false
	}

	// 如果有规则内容，尝试添加到规则引擎
	if product.Rule != "" {
		// 这里我们需要直接调用 ProductManager 的方法
		// 但由于访问限制，我们先尝试用 JSON 格式重新编码后加载
		jsonRule := fmt.Sprintf(`{"product":"%s","rule":"%s","rule_id":"%s","level":"%s","category":"%s","parent_category":"%s","softhard":"%s","company":"%s"}`,
			product.Name, product.Rule, product.RuleId, product.Level,
			product.Category, product.ParentCategory, product.SoftHard, product.Company)

		success := rulengine.Load(context.Background(), jsonRule, false)
		if !success {
			logger.Warnf("[ROBUST_LOADER] 第 %d 行规则添加到引擎失败: %s", lineNum, product.Name)
			return false
		}
	}

	return true
}

// GetStats 获取加载统计信息
func (r *RobustRuleLoader) GetStats() (success, skipped, errors int) {
	return r.successCount, r.skippedCount, r.errorCount
}
