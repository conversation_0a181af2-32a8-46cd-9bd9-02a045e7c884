package main

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"
)

// RuleStats 规则统计信息
type RuleStats struct {
	TotalLines      int            `json:"total_lines"`
	EmptyLines      int            `json:"empty_lines"`
	ValidRules      int            `json:"valid_rules"`
	InvalidRules    int            `json:"invalid_rules"`
	DuplicateRules  int            `json:"duplicate_rules"`
	UniqueProducts  map[string]int `json:"unique_products"`
	ErrorDetails    []string       `json:"error_details"`
}

// Rule 规则结构
type Rule struct {
	Product        string `json:"product"`
	Rule           string `json:"rule"`
	RuleID         string `json:"rule_id,omitempty"`
	Level          string `json:"level,omitempty"`
	Company        string `json:"company,omitempty"`
	Category       string `json:"category,omitempty"`
	ParentCategory string `json:"parent_category,omitempty"`
}

func main() {
	if len(os.Args) < 2 {
		log.Fatal("使用方法: go run rule_analyzer.go <规则文件路径>")
	}

	filePath := os.Args[1]
	stats := analyzeRules(filePath)
	
	// 输出统计结果
	fmt.Println("=== 规则文件分析结果 ===")
	fmt.Printf("总行数: %d\n", stats.TotalLines)
	fmt.Printf("空行数: %d\n", stats.EmptyLines)
	fmt.Printf("有效规则数: %d\n", stats.ValidRules)
	fmt.Printf("无效规则数: %d\n", stats.InvalidRules)
	fmt.Printf("重复规则数: %d\n", stats.DuplicateRules)
	fmt.Printf("唯一产品数: %d\n", len(stats.UniqueProducts))
	
	fmt.Println("\n=== 产品分布 (前10个) ===")
	count := 0
	for product, ruleCount := range stats.UniqueProducts {
		if count >= 10 {
			break
		}
		fmt.Printf("%s: %d 条规则\n", product, ruleCount)
		count++
	}
	
	if len(stats.ErrorDetails) > 0 {
		fmt.Println("\n=== 错误详情 (前10个) ===")
		for i, err := range stats.ErrorDetails {
			if i >= 10 {
				break
			}
			fmt.Printf("%d. %s\n", i+1, err)
		}
	}
	
	// 保存详细统计到JSON文件
	jsonData, _ := json.MarshalIndent(stats, "", "  ")
	outputFile := strings.TrimSuffix(filePath, ".json") + "_analysis.json"
	os.WriteFile(outputFile, jsonData, 0644)
	fmt.Printf("\n详细分析结果已保存到: %s\n", outputFile)
}

func analyzeRules(filePath string) *RuleStats {
	file, err := os.Open(filePath)
	if err != nil {
		log.Fatalf("打开文件失败: %v", err)
	}
	defer file.Close()

	stats := &RuleStats{
		UniqueProducts: make(map[string]int),
		ErrorDetails:   make([]string, 0),
	}
	
	seenRules := make(map[string]bool) // 用于检测重复规则
	scanner := bufio.NewScanner(file)
	lineNum := 0

	for scanner.Scan() {
		lineNum++
		stats.TotalLines++
		
		line := strings.TrimSpace(scanner.Text())
		if len(line) == 0 {
			stats.EmptyLines++
			continue
		}

		// 尝试解析JSON
		var rule Rule
		if err := json.Unmarshal([]byte(line), &rule); err != nil {
			stats.InvalidRules++
			stats.ErrorDetails = append(stats.ErrorDetails, 
				fmt.Sprintf("第%d行JSON解析失败: %v", lineNum, err))
			continue
		}

		// 检查必要字段
		if rule.Product == "" || rule.Rule == "" {
			stats.InvalidRules++
			stats.ErrorDetails = append(stats.ErrorDetails, 
				fmt.Sprintf("第%d行缺少必要字段(product或rule)", lineNum))
			continue
		}

		// 生成规则唯一标识
		ruleKey := fmt.Sprintf("%s:%s", rule.Product, rule.Rule)
		if seenRules[ruleKey] {
			stats.DuplicateRules++
			stats.ErrorDetails = append(stats.ErrorDetails, 
				fmt.Sprintf("第%d行发现重复规则: %s", lineNum, rule.Product))
			continue
		}
		
		seenRules[ruleKey] = true
		stats.ValidRules++
		stats.UniqueProducts[rule.Product]++
	}

	if err := scanner.Err(); err != nil {
		log.Fatalf("读取文件失败: %v", err)
	}

	return stats
}
