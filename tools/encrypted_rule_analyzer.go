package main

import (
	"bufio"
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"strings"
)

// EncryptedRuleStats 加密规则统计信息
type EncryptedRuleStats struct {
	IsEncrypted       bool   `json:"is_encrypted"`
	TotalFileSize     int64  `json:"total_file_size"`
	SignaturePart     string `json:"signature_part"`
	Base64Part        string `json:"base64_part"`
	Base64Size        int    `json:"base64_size"`
	DecodedSize       int    `json:"decoded_size"`
	EstimatedLines    int    `json:"estimated_lines"`
	CannotDecrypt     bool   `json:"cannot_decrypt"`
	DecryptionError   string `json:"decryption_error,omitempty"`
}

func main() {
	if len(os.Args) < 2 {
		log.Fatal("使用方法: go run encrypted_rule_analyzer.go <加密规则文件路径>")
		log.Fatal("示例: go run encrypted_rule_analyzer.go sutra/gosutra/resources/rules.json")
	}

	filePath := os.Args[1]
	fmt.Printf("正在分析加密规则文件: %s\n", filePath)
	
	stats := analyzeEncryptedRules(filePath)
	
	// 输出统计结果
	fmt.Println("\n=== 加密规则文件分析结果 ===")
	fmt.Printf("文件大小: %d bytes\n", stats.TotalFileSize)
	fmt.Printf("是否为加密格式: %t\n", stats.IsEncrypted)
	
	if stats.IsEncrypted {
		fmt.Printf("签名部分: %s\n", stats.SignaturePart)
		fmt.Printf("Base64 编码大小: %d bytes\n", stats.Base64Size)
		fmt.Printf("Base64 解码后大小: %d bytes\n", stats.DecodedSize)
		fmt.Printf("预估规则行数: %d 行\n", stats.EstimatedLines)
		
		if stats.CannotDecrypt {
			fmt.Printf("⚠️  无法解密内容: %s\n", stats.DecryptionError)
			fmt.Println("💡 需要 RSA 私钥才能解密并分析具体规则内容")
		}
		
		// 计算压缩比
		if stats.DecodedSize > 0 {
			compressionRatio := float64(stats.Base64Size) / float64(stats.DecodedSize) * 100
			fmt.Printf("压缩比: %.2f%%\n", compressionRatio)
		}
		
	} else {
		fmt.Println("⚠️  文件不是预期的加密格式")
	}
	
	fmt.Println("\n=== 建议 ===")
	if stats.IsEncrypted && stats.CannotDecrypt {
		fmt.Println("1. 这是加密的规则文件，无法直接分析具体规则内容")
		fmt.Println("2. 可以通过运行你的 data_analysis 服务来查看加载日志")
		fmt.Println("3. 观察日志中的 'error line' 来了解解析失败的规则")
		fmt.Printf("4. 预估包含约 %d 行规则数据\n", stats.EstimatedLines)
	}
}

func analyzeEncryptedRules(filePath string) *EncryptedRuleStats {
	file, err := os.Open(filePath)
	if err != nil {
		log.Fatalf("打开文件失败: %v", err)
	}
	defer file.Close()

	// 获取文件大小
	fileInfo, err := file.Stat()
	if err != nil {
		log.Fatalf("获取文件信息失败: %v", err)
	}

	stats := &EncryptedRuleStats{
		TotalFileSize: fileInfo.Size(),
	}

	// 读取文件内容
	scanner := bufio.NewScanner(file)
	var content strings.Builder
	for scanner.Scan() {
		content.WriteString(scanner.Text())
	}

	if err := scanner.Err(); err != nil {
		log.Fatalf("读取文件失败: %v", err)
	}

	fileContent := content.String()
	
	// 检查是否为加密格式 (signature:base64_content)
	parts := strings.SplitN(fileContent, ":", 2)
	if len(parts) != 2 {
		stats.IsEncrypted = false
		return stats
	}

	stats.IsEncrypted = true
	stats.SignaturePart = parts[0]
	stats.Base64Part = parts[1][:min(50, len(parts[1]))] + "..." // 只显示前50个字符
	stats.Base64Size = len(parts[1])

	// 尝试 Base64 解码
	decoded, err := base64.StdEncoding.DecodeString(parts[1])
	if err != nil {
		stats.CannotDecrypt = true
		stats.DecryptionError = fmt.Sprintf("Base64 解码失败: %v", err)
		return stats
	}

	stats.DecodedSize = len(decoded)
	
	// 由于没有 RSA 私钥，无法进一步解密
	stats.CannotDecrypt = true
	stats.DecryptionError = "需要 RSA 私钥进行解密"
	
	// 根据解码后的大小估算规则行数
	// 假设每条规则平均 200-300 bytes (MessagePack 格式)
	avgRuleSize := 250
	stats.EstimatedLines = stats.DecodedSize / avgRuleSize
	
	return stats
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
